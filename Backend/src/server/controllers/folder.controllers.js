import { sequelize } from "../database/dbConnection.js";
import Folder from '../models/folder.model.js';
import View from "../models/view.model.js";
import Workflow from "../models/workflow.model.js";
import { Op } from "sequelize";
export const createFolder = async (req, res) => {
  try {
      const systems = Array.isArray(req.body.systems) ? req.body.systems: JSON.parse(req.body.systems || '[]');
      let systemIds = [];
      if(systems && systems.length){
        systemIds = systems.map(item => item.systemId);
      }
        let dataToSave = {
            name: req.body.name,
            user_id: req.user.id,
            systems_id: systemIds,
        }
        if(req.body.parent_id){
            dataToSave.parent_id = req.body.parent_id
        }
        if(req.body.flowType){
          dataToSave.type = req.body.flowType
        }
        const folder = await Folder.create(dataToSave);
        // console.log(folder);

        return res.status(201).json({
            message: 'Folder created successfully',
            data: folder
        });
    } catch (error) {
        console.error('Error creating folder:', error.message);
        return res.status(500).json({ error: 'Failed to create folder' });
    }
};

export const getAllFolders = async (req, res) => {
    try {
      const systemsIdParam = req.query?.systems_id;
      const whereConditions = { 
        user_id: req.user.id, 
        parent_id: null ,
        hide_for_s3: false,
        type : 'workflow'
      };
      
      // If systemsNameParam exists, add the condition
      if (systemsIdParam && typeof systemsIdParam === 'string') {
        const systemsIdArray = await systemsIdParam.split(',').map((name) => name.trim()); // Split and trim the values
        if (systemsIdArray.length > 0) {
          // Sort arrays to ensure order doesn't matter
          const systemsIdSortedArray = systemsIdArray.sort();
          
          // Create the correct array literal format
          const arrayLiteral = `{${systemsIdSortedArray.join(',')}}`;
          
          // Modify the whereConditions to explicitly refer to the Folder table's systems_name
          whereConditions.systems_id = sequelize.literal(
            `"Folder"."systems_id" @> '${arrayLiteral}' AND "Folder"."systems_id" <@ '${arrayLiteral}'`
          );
        }
      }

        const folders = await Folder.findAll({
          where: whereConditions,
          include: [
            {
              model: Folder,
              as: 'children',
              include: [{ model: Folder, as: 'children' }],
            },
          ],
          order: [['name', 'ASC']],
        });
            // const workflows = await Workflow.findAll({
      //   where: { user_id: req.user.id },
      // });

      const whereConditionsWorkflow = { 
        user_id: req.user.id,
        hide_for_s3: false
      };

      if (systemsIdParam && typeof systemsIdParam === 'string') {
        const systemsIdArray = systemsIdParam.split(',').map((name) => name.trim()); // Split and trim the values
        
        if (systemsIdArray.length > 0) {
          // Sort arrays to ensure order doesn't matter
          const sortedSystemsIdArray = systemsIdArray.sort();
          
          // Create the correct array literal format
          const arrayLiteral = `{${sortedSystemsIdArray.join(',')}}`;
          
          // Modify the whereConditions to explicitly refer to the Folder table's systems_name
          whereConditionsWorkflow.systems_id = sequelize.literal(
            `"workflows"."systems_id" @> '${arrayLiteral}' AND "workflows"."systems_id" <@ '${arrayLiteral}'`
          );
        }
      }
      
      const workflows = await Workflow.findAll({
        where: whereConditionsWorkflow,
        order: [['id', 'DESC']], // Sort workflows by ID in ascending order
      });
        
  const foldersPlain = folders.map((folder) => folder.toJSON());
      const workflowsPlain = workflows.map((workflow) => workflow.toJSON());
  
      // Function to attach workflows as children and set type correctly
      const attachWorkflows = (folder) => {
        // Ensure the folder has a children array
        folder.children = folder.children || [];
  
        // Add workflows as children with type 'file' if the workflow belongs to this folder
        workflowsPlain.forEach((workflow) => {
          if (workflow?.folder_id === folder.id) {
            folder.children.push({
              id: workflow.id,
              name: workflow.name,
              type: 'file', // Mark workflow as a 'file'
            });
          }
        });
  
        // Recursively handle child folders
        if (folder.children && folder.children.length) {
          folder.children = folder.children.map(attachWorkflows);
        }
  
        // Mark the folder itself with type 'folder'
        return {
          ...folder,
          type: folder.type == 'file' ? 'file' : 'folder', // Folder type
        };
      };
  
      // Apply the attachWorkflows function to each top-level folder
      const structureWithWorkflows = foldersPlain.map(attachWorkflows);
  
      // Now we need to add workflows that are at the root level (not inside any folder)
      const rootWorkflows = workflowsPlain.filter((workflow) => workflow.folder_id === null);
      // console.log('rootWorkflows :', rootWorkflows);
  
      // Add the root workflows (those not in any folder) to the structure
      const finalStructure = [
        ...structureWithWorkflows,
        ...rootWorkflows.map((workflow) => ({
          id: workflow.id,
          name: workflow.name,
          type: 'file', // Root-level workflow is of type 'file'
        })),
      ];
  
      return res.status(201).json({
        message: 'Folders and workflows fetched successfully',
        data: finalStructure,
      });

    } catch (error) {
        console.error('Error creating folder:', error.message);
        return res.status(500).json({ error: 'Failed to fetch folders' });
    }
};


export const getAllFoldersWithViews = async (req, res) => {
  try {
    const systemsIdParam = req.query?.systems_id;
    const whereConditions = {
      user_id: req.user.id,
      parent_id: null,
      hide_for_s3: false,
      type : 'view'
    };

    // Handle systems_id filter
    if (systemsIdParam && typeof systemsIdParam === 'string') {
      const systemsIdArray = systemsIdParam.split(',').map((name) => name.trim());
      if (systemsIdArray.length > 0) {
        const sorted = systemsIdArray.sort();
        const arrayLiteral = `{${sorted.join(',')}}`;

        whereConditions.systems_id = sequelize.literal(
          `"Folder"."systems_id" @> '${arrayLiteral}' AND "Folder"."systems_id" <@ '${arrayLiteral}'`
        );
      }
    }

    // Fetch folders with children
    const folders = await Folder.findAll({
      where: whereConditions,
      include: [
        {
          model: Folder,
          as: 'children',
          include: [{ model: Folder, as: 'children' }],
        },
      ],
      order: [['name', 'ASC']],
    });

    const whereConditionsView = {
      user_id: req.user.id,
      // hide_for_s3: false,
    };

    // if (systemsIdParam && typeof systemsIdParam === 'string') {
    //   const systemsIdArray = systemsIdParam.split(',').map((name) => name.trim());
    //   if (systemsIdArray.length > 0) {
    //     const sorted = systemsIdArray.sort();
    //     const arrayLiteral = `{${sorted.join(',')}}`;

    //     whereConditionsView.systems_id = sequelize.literal(
    //       `"views"."systems_id" @> '${arrayLiteral}' AND "views"."systems_id" <@ '${arrayLiteral}'`
    //     );
    //   }
    // }

    // Fetch views
    const views = await View.findAll({
      where: whereConditionsView,
      order: [['id', 'DESC']],
    });

    const foldersPlain = folders.map((folder) => folder.toJSON());
    const viewsPlain = views.map((view) => view.toJSON());

    // Attach views as children under folders
    const attachViews = (folder) => {
      folder.children = folder.children || [];

      viewsPlain.forEach((view) => {
        if (view?.folder_id === folder.id) {
          folder.children.push({
            id: view.id,
            name: view.name,
            type: 'file',
            flowType:'view'
          });
        }
      });

      if (folder.children && folder.children.length) {
        folder.children = folder.children.map(attachViews);
      }

      return {
        ...folder,
        type: folder.type === 'file' ? 'file' : 'folder',
        flowType: 'view'
      };
    };

    const structureWithViews = foldersPlain.map(attachViews);

    const rootViews = viewsPlain.filter((view) => view.folder_id === null);

    const finalStructure = [
      ...structureWithViews,
      ...rootViews.map((view) => ({
        id: view.id,
        name: view.name,
        type: 'file',
        flowType:'view'
      })),
    ];

    return res.status(200).json({
      message: 'Folders and views fetched successfully',
      data: finalStructure,
    });
  } catch (error) {
    console.error('Error fetching folders and views:', error.message);
    return res.status(500).json({ error: 'Failed to fetch folders and views' });
  }
};