import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op } from "sequelize";
import SystemMeta from "../models/systemMeta.model.js";
import { generateResponse } from "../utils/commonResponse.js";

export const createSystemMeta = async (req, res) => {
    try {
        const {
            system_id,
            batch_id,
            customer_batch_id,
            parent_batch_id,
            parent_system_id,
            start_time,
            end_time,
            quality
        } = req.body;

        // Validate required fields if needed
        if (!system_id && !batch_id) {
            return res.status(400).json({
                error: 'At least one of system_id or batch_id is required.'
            });
        }

        let dataToSave = {
            system_id,
            batch_id,
            customer_batch_id,
            parent_batch_id,
            parent_system_id,
            start_time: start_time || new Date(),
            end_time: end_time || new Date(),
            quality
        };

        const savedSystemMeta = await SystemMeta.create(dataToSave);

        return res.status(201).json({
            data: savedSystemMeta,
            message: "System meta data created successfully"
        });
    } catch (error) {
        console.log('Error creating system meta:', error);
        return res.status(500).json({
            error: 'Failed to create system meta data',
            details: error.message
        });
    }
};

export const getSystemMeta = async (req, res) => {
    try {
        const { id } = req.params;
        const {
            system_id,
            batch_id,
            customer_batch_id,
            parent_batch_id,
            parent_system_id
        } = req.query;

        let whereClause = {};

        if (id) {
            whereClause.id = id;
        }
        if (system_id) {
            whereClause.system_id = system_id;
        }
        if (batch_id) {
            whereClause.batch_id = batch_id;
        }
        if (customer_batch_id) {
            whereClause.customer_batch_id = customer_batch_id;
        }
        if (parent_batch_id) {
            whereClause.parent_batch_id = parent_batch_id;
        }
        if (parent_system_id) {
            whereClause.parent_system_id = parent_system_id;
        }

        const systemMetaData = await SystemMeta.findAll({
            where: whereClause,
            order: [['start_time', 'DESC']]
        });

        return res.status(200).json({
            data: systemMetaData,
            message: "System meta data retrieved successfully"
        });
    } catch (error) {
        console.log('Error retrieving system meta:', error);
        return res.status(500).json({
            error: 'Failed to retrieve system meta data',
            details: error.message
        });
    }
};

export const updateSystemMeta = async (req, res) => {
    try {
        const { id } = req.params;
        const {
            system_id,
            batch_id,
            customer_batch_id,
            parent_batch_id,
            parent_system_id,
            start_time,
            end_time,
            quality
        } = req.body;

        const systemMeta = await SystemMeta.findByPk(id);

        if (!systemMeta) {
            return res.status(404).json({ error: 'System meta data not found.' });
        }

        const updateData = {};
        if (system_id !== undefined) updateData.system_id = system_id;
        if (batch_id !== undefined) updateData.batch_id = batch_id;
        if (customer_batch_id !== undefined) updateData.customer_batch_id = customer_batch_id;
        if (parent_batch_id !== undefined) updateData.parent_batch_id = parent_batch_id;
        if (parent_system_id !== undefined) updateData.parent_system_id = parent_system_id;
        if (start_time !== undefined) updateData.start_time = start_time;
        if (end_time !== undefined) updateData.end_time = end_time;
        if (quality !== undefined) updateData.quality = quality;

        await systemMeta.update(updateData);

        return res.status(200).json({
            data: systemMeta,
            message: "System meta data updated successfully"
        });
    } catch (error) {
        console.log('Error updating system meta:', error);
        return res.status(500).json({
            error: 'Failed to update system meta data',
            details: error.message
        });
    }
};

export const deleteSystemMeta = async (req, res) => {
    try {
        const { id } = req.params;

        const systemMeta = await SystemMeta.findByPk(id);

        if (!systemMeta) {
            return res.status(404).json({ error: 'System meta data not found.' });
        }

        await systemMeta.destroy();

        return res.status(200).json({
            message: "System meta data deleted successfully"
        });
    } catch (error) {
        console.log('Error deleting system meta:', error);
        return res.status(500).json({ 
            error: 'Failed to delete system meta data',
            details: error.message 
        });
    }
};
