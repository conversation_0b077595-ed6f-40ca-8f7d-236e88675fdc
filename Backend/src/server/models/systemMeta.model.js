import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js';

class SystemMeta extends Model {}

SystemMeta.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    system_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    batch_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    customer_batch_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    parent_batch_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    parent_system_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    start_time: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: true,
    },
    end_time: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: true,
    },
    quality: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'SystemMeta',
    tableName: 'system_meta',
    timestamps: false, // Since we're handling timestamps manually
    underscored: true, // Use snake_case for column names in the database
  }
);

export default SystemMeta;
