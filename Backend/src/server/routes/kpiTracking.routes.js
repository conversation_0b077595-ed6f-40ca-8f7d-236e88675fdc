import express from 'express';
import multer from 'multer';
import { 
    getKpiTrackingData, 
    getKpiTrackingGraphData,
    uploadKpiCsv
} from '../controllers/kpiTracking.controllers.js';
import { authenticateToken } from '../middlewares/jwt.js';

const router = express.Router();

// Multer middleware for file uploads
const uploadMiddleware = multer({ dest: "uploads/" });

router.get('/', authenticateToken, getKpiTrackingData);
router.get('/graph', authenticateToken, getKpiTrackingGraphData);
router.post('/upload', uploadMiddleware.single("file"), authenticateToken, uploadKpiCsv);

export default router; 