DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'onboarding_status') THEN
        CREATE TYPE onboarding_status AS ENUM ('in_progress', 'completed');
    END IF;
END $$;


ALTER TABLE users
ADD COLUMN onboarding onboarding_status DEFAULT 'in_progress';


INSERT INTO "users" ("first_name", "last_name", "email", "password_hash", "is_active", "created_at", "updated_at",onboarding)
VALUES
  ('martin', 'lious', '<EMAIL>', 'martin@123', TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP,'in_progress')



----------------------------------------------------------------------------------------------------------------------------------
Functionality : system bassed workflow folder , file upload and csv mapping
Commit :  b4dbbbc5e016c2430593afef4d19d2bf8adc4443
Branch : jayveer/changes/global-settings 
query :  

ALTER TABLE folders
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE workflows 
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE "CSVFiles" 
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE "CSVFiles" 
ADD COLUMN compatibility BOOLEAN 


----------------------------------------------------------------------------------------------------------------------------------
Functionality : csv prefilled mapping history
Commit :  c1401a3e092499167c8992d7cfa8b34f52657dfe
Branch : jayveer/changes/global-settings 
query :

CREATE TABLE public.csv_mapping (
	id serial4 NOT NULL,
	columns_mapping jsonb NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	tenant_id int4 NULL
);

ALTER TABLE public.csv_mapping ADD CONSTRAINT fk_tenant_id FOREIGN KEY (tenant_id) REFERENCES public.tenants(id)
 ON DELETE CASCADE;




----------------------------------------------------------------------------------------------------------------------------------
Functionality : Golden parameters good sample bad sample total sample and total good sample percentage added in table
Commit :  
Branch : vikram/good-bad-sample-functionality
query :

 alter table golden_value_data add column cluster_data jsonb


Functionality : Golden parameters Filter bassed on filter_id only and remove query builder , filter saved along with systems name
Commit :  
Branch : jira-222/golden-parameters-filters
query :

    ALTER TABLE golden_value_data ADD COLUMN filter_id INTEGER;
	alter table workflow_filters add column systems_name _text DEFAULT '{}'::text[] NULL



Functionality : Save filter names and golden values as per filter id
Commit :  
Branch : jira-222/golden-parameters-filters
query :

	alter table workflows add column filter_id INT

	



---------------------------------------------------------------------------------------------------------------------------------------
Functionality : system bassed workflow folder , file upload and csv mapping
Commit :  b4dbbbc5e016c2430593afef4d19d2bf8adc4443
Branch : jayveer/changes/global-settings 
query :  

ALTER TABLE folders
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE workflows 
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE "CSVFiles" 
ADD COLUMN systems_name TEXT[] DEFAULT '{}'

ALTER TABLE "CSVFiles" 
ADD COLUMN compatibility BOOLEAN 


------------------------------------------------------------------------------------------------------------------------------------
Functionality : csv prefilled mapping history
Commit :  c1401a3e092499167c8992d7cfa8b34f52657dfe
Branch : jayveer/changes/global-settings 
query :

CREATE TABLE public.csv_mapping (
	id serial4 NOT NULL,
	columns_mapping jsonb NOT NULL,
	created_at timestamptz NOT NULL,
	updated_at timestamptz NOT NULL,
	tenant_id int4 NULL
);

ALTER TABLE public.csv_mapping ADD CONSTRAINT fk_tenant_id FOREIGN KEY (tenant_id) REFERENCES public.tenants(id)
 ON DELETE CASCADE;




---------------------------------------------------------------------------------------------------------------------------------------
Functionality : Golden parameters good sample bad sample total sample and total good sample percentage added in table
Commit :  
Branch : vikram/good-bad-sample-functionality
query :

 alter table golden_value_data add column cluster_data jsonb
 

 ---------------------------------------------------
 Functionality : Filter saved along with systems name, tenant_id, isDefaultSetting
 Commit :  
 Branch : jira-234/associate-filters
 query :

	ALTER TABLE workflow_filters
	ADD COLUMN systems INTEGER[] DEFAULT '{}'::INTEGER[], -- Array of integers for system IDs
	ADD COLUMN tenant_id INTEGER, -- Integer for tenant ID
	ADD COLUMN isDefaultSetting BOOLEAN NOT NULL DEFAULT FALSE; -- Boolean for default setting
//Need to use this query to change the type of systems column to integer[] after redux changes
	ALTER TABLE workflow_filters
	ALTER COLUMN systems TYPE INTEGER[] USING systems::INTEGER[]; 



----------------------------------------------------------------------------------------------------------------------------------------

Functionality : Change the system_name to system_id
Commit :  
Branch : rename-systems-name-to-system-id/dev
query :

	ALTER TABLE public.systems 
	ADD COLUMN created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
	ADD COLUMN updated_at timestamptz DEFAULT CURRENT_TIMESTAMP;


	ALTER TABLE systems 
	ADD COLUMN tenant_id int

	UPDATE systems SET tenant_id = 1


	ALTER TABLE systems
	ADD CONSTRAINT fk_system
	FOREIGN KEY (tenant_id) REFERENCES tenants(id)
	ON DELETE CASCADE;


	ALTER TABLE systems 
	ADD COLUMN tenant_id int

	ALTER TABLE systems
	ADD CONSTRAINT fk_system
	FOREIGN KEY (tenant_id) REFERENCES tenants(id)
	ON DELETE CASCADE;



	ALTER TABLE "CSVFiles"
	RENAME COLUMN systems_name TO systems_id;



	UPDATE "CSVFiles"
	SET systems_id = (
	SELECT array_agg(
		CASE 
		WHEN systems_id = 'VAT' THEN '3'
		WHEN systems_id = 'Testing' THEN '8'
		ELSE systems_id 
		END
	)
	FROM unnest(systems_id) AS systems_id
	)


	ALTER TABLE folders 
	RENAME COLUMN systems_name TO systems_id;


	UPDATE folders 
	SET systems_id = (
	SELECT array_agg(
		CASE 
		WHEN systems_id = 'VAT' THEN '3'
		WHEN systems_id = 'Testing' THEN '8'
		ELSE systems_id 
		END
	)
	FROM unnest(systems_id) AS systems_id
	)


	ALTER TABLE workflows 
	RENAME COLUMN systems_name TO systems_id;

	UPDATE workflows 
	SET systems_id = (
	SELECT array_agg(
		CASE 
		WHEN systems_id = 'VAT' THEN '3'
		WHEN systems_id = 'Testing' THEN '8'
		ELSE systems_id 
		END
	)
	FROM unnest(systems_id) AS systems_id
	)





ALTER TABLE workflows 
ADD COLUMN custom_filter_id INTEGER NULL REFERENCES workflow_filters(id) 
ON DELETE SET NULL ON UPDATE CASCADE;


ALTER TABLE workflow_filters 
ADD COLUMN filter_type VARCHAR(255);

----------------------------------------------------------------------------------------------------------------------------------------

Functionality : Sharable Runs
Commit :  
Branch : PRDM-250/sharable-runs
query :
ALTER TABLE workflow_shared
DROP CONSTRAINT fk_workflow_shared;
ALTER TABLE workflow_shared
ALTER COLUMN share_type_id TYPE varchar(35);

----------------------------------------------------------------------------------------------------------------------------------------

Functionality : File Validation
Commit :  
Branch : jayveer/changes/file-validation
query :
ALTER TABLE "CSVFiles" 
ADD COLUMN type_of_file TEXT[];

-- Change the type_of_file column from enum to array
ALTER TABLE "CSVFiles" 
DROP COLUMN type_of_file;

-- Add new column as text array
ALTER TABLE "CSVFiles" 
ADD COLUMN type_of_file TEXT[] DEFAULT '{"processes"}'::TEXT[] NOT NULL;

-- Drop the now unused enum type (optional, can be done after verifying all is working)
-- DROP TYPE file_type_enum;
ADD COLUMN type_of_file file_type_enum NOT NULL DEFAULT 'processes';






----------------------------------------------------------------------------------------------------------------------------------------

Functionality : save bucketization result
Commit :  
Branch : jira-283/material-parameters/dev
query :


CREATE TABLE bucketization_value_data (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES "users"(id),
    name VARCHAR NOT NULL,
    workflow_id INTEGER REFERENCES workflows(id) ON DELETE CASCADE,
    filter_id INTEGER,
    bucketization_statistical_value JSON NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

----------------------------------------------------------------------------------------------------------------------------------------

Functionality : Clustering-inference
Commit :  
Branch : PRDM-256/clustering-inference
query :

ALTER TABLE "CSVFiles" 
ADD COLUMN clustering_data_response TEXT 
CHECK (clustering_data_response IN ('in_progress', 'completed', 'failed')) 
DEFAULT NULL;




-- jira-333 (Workflow View module implementation)


CREATE TABLE public.pannel_types (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT pannel_types_pkey PRIMARY KEY (id)
);



CREATE TABLE public."views" (
	id serial4 NOT NULL,
	"name" varchar(255) NOT NULL,
	user_id int4 NOT NULL,
	folder_id int4 NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	"structure" json DEFAULT '{}'::json NOT NULL,
	csvfile_id int4 NULL,
	CONSTRAINT views_pkey PRIMARY KEY (id)
);


-- public."views" foreign keys

ALTER TABLE public."views" ADD CONSTRAINT "CSVFiles" FOREIGN KEY (csvfile_id) REFERENCES public."CSVFiles"(id) ON DELETE SET NULL;
ALTER TABLE public."views" ADD CONSTRAINT fk_folder FOREIGN KEY (folder_id) REFERENCES public.folders(id) ON DELETE CASCADE;
ALTER TABLE public."views" ADD CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;



CREATE TABLE public.view_pannel (
	id int4 DEFAULT nextval('views_components_id_seq'::regclass) NOT NULL,
	views_id int4 NULL,
	"configuration" json NOT NULL,
	created_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamp DEFAULT CURRENT_TIMESTAMP NULL,
	pannel_type_id int4 NULL,
	CONSTRAINT views_components_pkey PRIMARY KEY (id)
);


-- public.view_pannel foreign keys

ALTER TABLE public.view_pannel ADD CONSTRAINT views_components_views_id_fkey FOREIGN KEY (views_id) REFERENCES public."views"(id) ON DELETE CASCADE;
ALTER TABLE public.view_pannel ADD CONSTRAINT views_pannel_pannel_type_id_fkey FOREIGN KEY (pannel_type_id) REFERENCES public.pannel_types(id) ON DELETE SET NULL;



ALTER TABLE public.folders
ADD COLUMN type VARCHAR(100);

UPDATE public.folders
SET "type" = 'workflow'
WHERE "type" IS NULL;



INSERT INTO pannel_types ("name") 
VALUES ('Overview'), ('Histogram'), ('Time Series'), ('Data Table');




ALTER TABLE users
ADD COLUMN two_factor_secret VARCHAR(255) NULL,
ADD COLUMN two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN temp_2fa_secret VARCHAR(255) NULL,
ADD COLUMN temp_2fa_enabled BOOLEAN NULL;


ALTER TABLE users
ADD COLUMN email_verification_enabled BOOLEAN DEFAULT TRUE;




-------------------------------------------------------------------------

CREATE TABLE system_meta (
    id serial4 NOT NULL PRIMARY KEY,
    system_id INT,
    batch_id VARCHAR(255),
    customer_batch_id VARCHAR(255),
    parent_batch_id VARCHAR(255),
    parent_system_id INT,
    start_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    end_time timestamp DEFAULT CURRENT_TIMESTAMP NULL,
    quality JSON
);