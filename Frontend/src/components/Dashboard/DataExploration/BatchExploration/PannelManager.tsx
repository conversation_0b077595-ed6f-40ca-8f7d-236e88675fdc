import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Table, 
  Button, 
  Space, 
  message, 
  Popconfirm, 
  Tag,
  Input,
  Select,
  Form,
  Card,
  Row,
  Col
} from 'antd';
import { 
  DeleteOutlined, 
  EditOutlined, 
  PlusOutlined,
  EyeOutlined,
  SaveOutlined
} from '@ant-design/icons';
import Notiflix from 'notiflix';
import { deleteRequest, getRequest, putRequest } from '../../../../utils/apiHandler';

const { Search } = Input;
const { Option } = Select;

interface Pannel {
  id: number;
  name: string;
  configurations: any;
  data_source_path: string;
  data_source: string;
  user_id: number;
  created_at: string;
  updated_at: string;
  csvFile?: {
    id: string;
    file_name: string;
    file_path: string;
  };
}

interface PannelManagerProps {
  visible: boolean;
  onClose: () => void;
  onSelectPannel?: (pannel: Pannel) => void;
  selectedFile?: any;
}

const PannelManager: React.FC<PannelManagerProps> = ({
  visible,
  onClose,
  onSelectPannel,
  selectedFile
}) => {
  const [pannels, setPannels] = useState<Pannel[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filterDataSource, setFilterDataSource] = useState<string>('all');
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingPannel, setEditingPannel] = useState<Pannel | null>(null);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewPannel, setPreviewPannel] = useState<Pannel | null>(null);
  const [form] = Form.useForm();

  // Fetch pannels
  const fetchPannels = async () => {
    setLoading(true);
    try {
      const response = await getRequest('/pannel/list');
      if (response.data && response.data.status === 200) {
        setPannels(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching pannels:', error);
      message.error('Failed to fetch pannels');
    } finally {
      setLoading(false);
    }
  };

  // Fetch pannels by data source
  const fetchPannelsByDataSource = async (csvFileId: string) => {
    setLoading(true);
    try {
      const response = await getRequest(`/pannel/data-source/${csvFileId}`);
      if (response.data && response.data.status === 200) {
        setPannels(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching pannels by data source:', error);
      message.error('Failed to fetch pannels');
    } finally {
      setLoading(false);
    }
  };

  // Delete pannel
  const deletePannel = async (id: number) => {
    try {
      Notiflix.Loading.circle('Deleting pannel...');
      const response = await deleteRequest(`/pannel/${id}`);
      if (response.data && response.data.status === 200) {
        message.success('Pannel deleted successfully');
        fetchPannels();
      }
    } catch (error) {
      console.error('Error deleting pannel:', error);
      message.error('Failed to delete pannel');
    } finally {
      Notiflix.Loading.remove();
    }
  };

  // Update pannel
  const updatePannel = async (values: any) => {
    if (!editingPannel) return;

    try {
      Notiflix.Loading.circle('Updating pannel...');
      const response = await putRequest(`/pannel/${editingPannel.id}`, {
        name: values.name,
        configurations: editingPannel.configurations
      });
      
      if (response.data && response.data.status === 200) {
        message.success('Pannel updated successfully');
        setEditModalVisible(false);
        setEditingPannel(null);
        form.resetFields();
        fetchPannels();
      }
    } catch (error) {
      console.error('Error updating pannel:', error);
      message.error('Failed to update pannel');
    } finally {
      Notiflix.Loading.remove();
    }
  };

  // Filter pannels
  const filteredPannels = pannels.filter(pannel => {
    const matchesSearch = pannel.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesDataSource = filterDataSource === 'all' || 
      (filterDataSource === 'current' && selectedFile && pannel.data_source_path === selectedFile.csv_id) ||
      (filterDataSource === 'batch' && pannel.data_source === 'batch');
    return matchesSearch && matchesDataSource;
  });

  // Get panel type from configurations
  const getPanelType = (configurations: any) => {
    return configurations?.panelType || 'Unknown';
  };

  // Get panel type color
  const getPanelTypeColor = (panelType: string) => {
    switch (panelType) {
      case 'TimeSeriesPanel': return 'blue';
      case 'OverviewPanel': return 'green';
      case 'HistogramPanel': return 'orange';
      case 'DataTablePanel': return 'purple';
      default: return 'default';
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: Pannel, b: Pannel) => a.name.localeCompare(b.name),
      render: (text: string, record: Pannel) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.configurations?.title || 'No title'}
          </div>
        </div>
      ),
    },
    {
      title: 'Panel Type',
      key: 'panelType',
      render: (_: any, record: Pannel) => {
        const panelType = getPanelType(record.configurations);
        return <Tag color={getPanelTypeColor(panelType)}>{panelType}</Tag>;
      },
    },
    {
      title: 'Data Source',
      dataIndex: ['csvFile', 'file_name'],
      key: 'dataSource',
      render: (text: string) => text || 'No data source',
    },
    {
      title: 'Data Source Type',
      dataIndex: 'data_source',
      key: 'dataSourceType',
      render: (text: string) => <Tag color="green">{text}</Tag>,
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text: string) => new Date(text).toLocaleDateString(),
      sorter: (a: Pannel, b: Pannel) => 
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Pannel) => (
        <Space size="middle">
          {onSelectPannel && (
            <Button
              type="primary"
              size="small"
              icon={<PlusOutlined />}
              onClick={() => onSelectPannel(record)}
            >
              Use
            </Button>
          )}
          <Button
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setPreviewPannel(record);
              setPreviewModalVisible(true);
            }}
          >
            Preview
          </Button>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingPannel(record);
              form.setFieldsValue({ name: record.name });
              setEditModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Popconfirm
            title="Delete Pannel"
            description="Are you sure you want to delete this pannel?"
            onConfirm={() => deletePannel(record.id)}
            okText="Yes"
            cancelText="No"
            okButtonProps={{ danger: true }}
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    if (visible) {
      if (selectedFile && filterDataSource === 'current') {
        fetchPannelsByDataSource(selectedFile.csv_id);
      } else {
        fetchPannels();
      }
    }
  }, [visible, selectedFile, filterDataSource]);

  return (
    <>
      <Modal
        title="Pannel Manager"
        open={visible}
        onCancel={onClose}
        width={1200}
        footer={[
          <Button key="close" onClick={onClose}>
            Close
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Search
              placeholder="Search pannels..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 300 }}
            />
            <Select
              value={filterDataSource}
              onChange={setFilterDataSource}
              style={{ width: 200 }}
            >
              <Option value="all">All Data Sources</Option>
              {selectedFile && (
                <Option value="current">Current File Only</Option>
              )}
              <Option value="batch">Batch Only</Option>
            </Select>
            <Button onClick={fetchPannels} loading={loading}>
              Refresh
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={filteredPannels}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} pannels`,
          }}
        />
      </Modal>

      {/* Edit Modal */}
      <Modal
        title="Edit Pannel"
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingPannel(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={updatePannel}
        >
          <Form.Item
            name="name"
            label="Pannel Name"
            rules={[{ required: true, message: 'Please enter a pannel name' }]}
          >
            <Input placeholder="Enter pannel name" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Update
              </Button>
              <Button onClick={() => {
                setEditModalVisible(false);
                setEditingPannel(null);
                form.resetFields();
              }}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Preview Modal */}
      <Modal
        title={`Preview: ${previewPannel?.name}`}
        open={previewModalVisible}
        onCancel={() => {
          setPreviewModalVisible(false);
          setPreviewPannel(null);
        }}
        width={800}
        footer={[
          <Button key="close" onClick={() => {
            setPreviewModalVisible(false);
            setPreviewPannel(null);
          }}>
            Close
          </Button>
        ]}
      >
        {previewPannel && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <Card title="Basic Information" size="small">
                  <p><strong>Name:</strong> {previewPannel.name}</p>
                  <p><strong>Panel Type:</strong> {getPanelType(previewPannel.configurations)}</p>
                  <p><strong>Data Source:</strong> {previewPannel.csvFile?.file_name || 'No data source'}</p>
                  <p><strong>Data Source Type:</strong> {previewPannel.data_source}</p>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="Position & Layout" size="small">
                  <p><strong>X:</strong> {previewPannel.configurations?.position?.x || 0}</p>
                  <p><strong>Y:</strong> {previewPannel.configurations?.position?.y || 0}</p>
                  <p><strong>Width:</strong> {previewPannel.configurations?.position?.w || 6}</p>
                  <p><strong>Height:</strong> {previewPannel.configurations?.position?.h || 6}</p>
                </Card>
              </Col>
            </Row>
            <Card title="Configuration" size="small" style={{ marginTop: 16 }}>
              <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px', fontSize: '12px' }}>
                {JSON.stringify(previewPannel.configurations, null, 2)}
              </pre>
            </Card>
          </div>
        )}
      </Modal>
    </>
  );
};

export default PannelManager;
