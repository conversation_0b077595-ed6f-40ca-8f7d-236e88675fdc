import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Card, Typography } from 'antd';

const { Title, Text, Paragraph } = Typography;

interface ZoomDebugHelperProps {
  dateFilter?: any;
}

const ZoomDebugHelper: React.FC<ZoomDebugHelperProps> = ({ dateFilter }) => {
  
  useEffect(() => {
    // Add a global listener for console logs to help track the zoom flow
    const originalConsoleLog = console.log;
    console.log = (...args) => {
      // Only intercept our debug logs
      if (args[0] && typeof args[0] === 'string' && args[0].includes('🔍')) {
        originalConsoleLog('🔍 ZOOM DEBUG INTERCEPTED:', ...args);
      }
      originalConsoleLog(...args);
    };

    return () => {
      console.log = originalConsoleLog;
    };
  }, []);

  const clearConsole = () => {
    console.clear();
    console.log('🔍 CONSOLE CLEARED - Ready for zoom debugging');
  };

  const logCurrentState = () => {
    console.log('🔍 CURRENT STATE SNAPSHOT:', {
      timestamp: new Date().toISOString(),
      source: 'ZoomDebugHelper.logCurrentState',
      dateFilter: dateFilter,
      location: window.location.href
    });
  };

  return (
    <Card 
      size="small" 
      style={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        zIndex: 1000, 
        width: 300,
        backgroundColor: '#f0f8ff',
        border: '2px solid #1890ff'
      }}
    >
      <Title level={5} style={{ margin: 0, color: '#1890ff' }}>
        🔍 Zoom Debug Helper
      </Title>
      
      <Paragraph style={{ margin: '8px 0', fontSize: '12px' }}>
        <Text strong>Current Date Filter:</Text><br />
        <Text code style={{ fontSize: '10px' }}>
          {JSON.stringify(dateFilter, null, 2)}
        </Text>
      </Paragraph>

      <div style={{ display: 'flex', gap: '8px', flexDirection: 'column' }}>
        <Button size="small" onClick={clearConsole} type="primary">
          Clear Console
        </Button>
        <Button size="small" onClick={logCurrentState}>
          Log Current State
        </Button>
      </div>

      <Paragraph style={{ margin: '8px 0 0 0', fontSize: '10px', color: '#666' }}>
        <Text strong>Instructions:</Text><br />
        1. Clear console<br />
        2. Use zoom tool on time series graph<br />
        3. Check console for 🔍 debug logs<br />
        4. Look for time discrepancies
      </Paragraph>
    </Card>
  );
};

export default ZoomDebugHelper;
