import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { ColumnSelection, DateFilter } from '../../types';
import { PanelFilter } from '../../FilterTypes';
import { Spin, Empty, Tabs, Switch, Tooltip, Button } from 'antd';
import { ClearOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useSelector, useDispatch } from "react-redux";
import { addAnnotation, setActiveColumnName, setAnnotations } from '../../../../../Redux/slices/annotationSlice';
import { addOperation, setActiveColumnName as setOperationActiveColumnName ,setOperations} from '../../../../../Redux/slices/operationSlice';
import { postRequest } from '../../../../../utils/apiHandler';
import AppliedFilters from '../../AppliedFilters';
interface TimeSeriesPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  conditionalFilters?: PanelFilter[]; // Added missing prop
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string,data?:any) => void;
  onColumnSelection?: (indices: number[], headers: string[]) => void; // Added missing prop
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void; // Added missing prop
  onAddFilter?: (filter: PanelFilter) => void; // Added missing prop
  isFullScreen?: boolean;
  onRemoveFilter?: (filterId: string, panelId: string) => void;
  onClearAllFilters?: () => void;
}

const TimeSeriesPanel: React.FC<TimeSeriesPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  conditionalFilters = [], // Added missing prop
  onZoomSelection,
  onColumnSelection, // Added missing prop
  onDateFilterChange, // Added missing prop
  onAddFilter, // Added missing prop
  isFullScreen = false,
  onRemoveFilter,
  onClearAllFilters
}) => {

  console.log('original data in (Time Series Component)', data);
  console.log('data after filteredData in (Time Series Component)', filteredData);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);
  const [plotHeight, setPlotHeight] = useState(300);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [activeKey, setActiveKey] = useState("0");
  const [drawMode, setDrawMode] = useState<string>('select'); // Add state for tracking draw mode
  const [showFullData, setShowFullData] = useState<boolean>(false); // Toggle between filtered and full data view

  // Selection popup state
  const [selectionPopup, setSelectionPopup] = useState<{
    visible: boolean;
    x: number;
    y: number;
    selectionData: any;
  }>({
    visible: false,
    x: 0,
    y: 0,
    selectionData: null
  });
  const annotations = useSelector((state: any) => state.annotations);
  const selectedAnnotations = useSelector((state: any) => state.annotations.annotations);
  const operationsState = useSelector((state: any) => state.operations);
  const dispatch = useDispatch();
  const selectSystems = useSelector((state: any) => state.systems.systems);

  // Add ref to track the last fetched column to prevent unnecessary API calls
  const lastFetchedColumnRef = useRef<string | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const updateSize = () => {
      const rect = container.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      if (width > 0 && height > 0) {
        setContainerSize({ width, height });
        const heightRatio = isFullScreen ? 1.0 : 0.8;
        setPlotHeight(Math.floor(height * heightRatio));
        setLayout((prev: any) => ({
          ...prev,
          height: Math.floor(height * heightRatio)
        }));
        // Set chart ready after a small delay to ensure DOM is stable
        setTimeout(() => setIsChartReady(true), 100);
      }
    };

    // Initial size update
    updateSize();

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setContainerSize({ width, height });
          const heightRatio = isFullScreen ? 1.0 : 0.8;
          setPlotHeight(Math.floor(height * heightRatio));
          setLayout((prev: any) => ({
            ...prev,
            height: Math.floor(height * heightRatio)
          }));
          // Set chart ready after a small delay to ensure DOM is stable
          setTimeout(() => setIsChartReady(true), 100);
        }
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, [isFullScreen]);


  // pass the intial default selected column name
  useEffect(()=>{
    if (columnTabs.length > 0) {
      dispatch(setActiveColumnName(columnTabs[parseInt(activeKey)].name))
      dispatch(setOperationActiveColumnName(columnTabs[parseInt(activeKey)].name))
    }
  },[])





  const setZoomLayout = () => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      console.log('ZOOM LAYOUT - No valid column tabs or activeKey');
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];
    console.log('ZOOM LAYOUT - Selected Tab:', selectedTab.name);

    // Check if we have backend extremes data
    const dataToUse = filteredData || data;
    let extremes = null;

    if (dataToUse?.echartsData?.extremes) {
      // Use backend-calculated extremes
      console.log('ZOOM LAYOUT - Using backend extremes');
      extremes = dataToUse.echartsData.extremes;
    } else {
      // Fallback to frontend calculation
      console.log('ZOOM LAYOUT - Calculating extremes on frontend');
      const filteredExtremes = filteredData && Array.isArray(filteredData) ?
        getExtremes(filteredData, selectedTab.name) : null;
      const originalExtremes = Array.isArray(data) ? getExtremes(data, selectedTab.name) : null;
      extremes = filteredExtremes || originalExtremes;
    }

    console.log('ZOOM LAYOUT - Extremes data:', extremes);

    if (extremes && extremes[selectedTab.name] && extremes.DateTime) {
      console.log('ZOOM LAYOUT - Setting zoom layout based on extremes:', extremes);

      // For x-axis, use date filter if available, otherwise use data extremes
      const xRange = dateFilter.startDate && dateFilter.endDate ?
        [dateFilter.startDate, dateFilter.endDate] :
        [extremes.DateTime.min, extremes.DateTime.max];

      // Calculate y-axis range with proper padding
      const yMin = extremes[selectedTab.name].min as any;
      const yMax = extremes[selectedTab.name].max as any;

      console.log('ZOOM LAYOUT - Y-axis range calculation:', {
        min: yMin,
        max: yMax,
        range: yMax - yMin
      });

      setLayout((prev: any) => {
        const newLayout = {
          ...prev,
          xaxis: {
            ...(prev?.xaxis || {}),
            range: xRange,
            fixedrange: false, // Allow x-axis zooming
          },
          yaxis: {
            ...(prev?.yaxis || {}),
            // Set the range but keep it fixed
            range: [yMin, yMax],
            fixedrange: true, // Lock y-axis when zooming
            autorange: false, // Disable autorange to use our custom range
          },
          dragmode: drawMode,
        };

        console.log('ZOOM LAYOUT - New layout being set:', {
          xaxisRange: newLayout.xaxis.range,
          yaxisRange: newLayout.yaxis.range,
          yaxisAutorange: newLayout.yaxis.autorange
        });

        return newLayout;
      });
    } else {
      console.log('ZOOM LAYOUT - No extremes available, cannot set layout');
    }
  }


  const getExtremes = (data: any[], key: string) => {
    console.log('GET EXTREMES - Starting calculation for key:', key);

    // Add comprehensive data validation
    if (!data || !Array.isArray(data) || data.length === 0 || !key) {
      return null;
    }

    let minKeyVal = Infinity;
    let maxKeyVal = -Infinity;
    let minDate = new Date(data[0].DateTime);
    let maxDate = new Date(data[0].DateTime);

    // Count valid values for debugging
    let validValueCount = 0;
    let nanValueCount = 0;

    for (let item of data) {
      const keyVal = parseFloat(item[key]);
      const date = new Date(item.DateTime);

      if (!isNaN(keyVal)) {
        minKeyVal = Math.min(minKeyVal, keyVal);
        maxKeyVal = Math.max(maxKeyVal, keyVal);
        validValueCount++;
      } else {
        nanValueCount++;
      }

      if (!isNaN(date.getTime())) {
        if (date < minDate) minDate = date;
        if (date > maxDate) maxDate = date;
      }
    }

    console.log('GET EXTREMES - Value counts:', {
      validValues: validValueCount,
      nanValues: nanValueCount,
      totalRows: data.length
    });

    console.log('GET EXTREMES - Raw extremes:', {
      minKeyVal: minKeyVal,
      maxKeyVal: maxKeyVal,
      minDate: minDate,
      maxDate: maxDate
    });

    // Add padding to the y-axis range to avoid stretched appearance
    // Calculate a padding of 20% of the range on both sides (increased from 10%)
    const range = maxKeyVal - minKeyVal;

    // Special case for when min and max are the same (flat line)
    if (range === 0 || range < 0.000001) {
      console.log('GET EXTREMES - Flat line detected, using special padding');
      // Use 50% of the value as padding, with a minimum of 1
      const flatLinePadding = Math.max(1, Math.abs(maxKeyVal) * 0.5);

      // Apply padding to min and max values
      const paddedMin = minKeyVal - flatLinePadding;
      const paddedMax = maxKeyVal + flatLinePadding;

      console.log('GET EXTREMES - Flat line padding:', {
        value: maxKeyVal,
        padding: flatLinePadding,
        paddedMin: paddedMin,
        paddedMax: paddedMax
      });

      const result = {
        [key]: {
          min: paddedMin,
          max: paddedMax,
          rawMin: minKeyVal,
          rawMax: maxKeyVal
        },
        DateTime: {
          min: minDate.toISOString(),
          max: maxDate.toISOString()
        }
      };

      console.log('GET EXTREMES - Final result for flat line:', result);
      return result;
    }

    const padding = range * 0.2;

    // If range is very small (near zero), use a fixed padding
    const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

    // Apply padding to min and max values
    const paddedMin = minKeyVal - effectivePadding;
    const paddedMax = maxKeyVal + effectivePadding;

    console.log('GET EXTREMES - Padding calculation:', {
      range: range,
      padding: padding,
      effectivePadding: effectivePadding,
      paddedMin: paddedMin,
      paddedMax: paddedMax
    });

    const result = {
      [key]: {
        min: paddedMin,
        max: paddedMax,
        rawMin: minKeyVal,
        rawMax: maxKeyVal
      },
      DateTime: {
        min: minDate.toISOString(),
        max: maxDate.toISOString()
      }
    };

    console.log('GET EXTREMES - Final result:', result);
    return result;
  };

  // Add annotations and operations to the backend ECharts option
  const addAnnotationsToOption = (baseOption: any, columnName: string) => {
    if (!baseOption || !baseOption.series) return baseOption;

    // Get annotations and operations for current column
    const columnAnnotations = selectedAnnotations.find((ann: any) => ann.columnName === columnName)?.annotations?.filter((a: any) => a.selected === true && a.applyAsFilter !== true) || [];
    const columnOperations = operationsState?.operations?.find((op: any) => op.columnName === columnName)?.operations?.filter((o: any) => o.selected === true) || [];

    // Create markArea for annotations (red rectangles)
    const markAreas = columnAnnotations.map((annotation: any) => [
      {
        xAxis: new Date(annotation.x0).getTime(),
        yAxis: annotation.y0
      },
      {
        xAxis: new Date(annotation.x1).getTime(),
        yAxis: annotation.y1
      }
    ]);

    // Create markLine for operations (green horizontal lines)
    const markLines: any[] = [];
    columnOperations.forEach((operation: any, opIndex: number) => {
      // Add line for y0 (lower bound) - no label
      markLines.push({
        yAxis: operation.y0,
        lineStyle: {
          color: 'rgba(0, 128, 0, 0.7)',
          width: 2,
          type: 'dashed'
        },
        label: {
          show: false
        }
      });

      // Add line for y1 (upper bound) - with operation name label on left side
      markLines.push({
        yAxis: operation.y1,
        lineStyle: {
          color: 'rgba(0, 128, 0, 0.7)',
          width: 2,
          type: 'dashed'
        },
        label: {
          show: true,
          position: 'start',
          formatter: operation?.label?.text || `Operation ${opIndex + 1}`,
          color: 'rgba(0, 128, 0, 0.8)',
          fontSize: 12,
          fontWeight: 'bold',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderColor: 'rgba(0, 128, 0, 0.7)',
          borderWidth: 1,
          borderRadius: 3,
          padding: [2, 4]
        }
      });
    });

    // Clone the base option and add annotations/operations to the first series
    const enhancedOption = { ...baseOption };
    enhancedOption.series = baseOption.series.map((series: any, index: number) => {
      const enhancedSeries = { ...series };

      // Add markArea for annotations (only for the first series to avoid duplicates)
      if (index === 0 && markAreas.length > 0) {
        enhancedSeries.markArea = {
          silent: true,
          itemStyle: {
            color: 'rgba(255, 0, 0, 0.1)',
            borderColor: 'rgba(255, 0, 0, 0.7)',
            borderWidth: 2
          },
          label: {
            show: true,
            position: 'inside',
            formatter: function(params: any) {
              const annotation = columnAnnotations[params.dataIndex];
              return annotation?.label?.text || 'Anomaly';
            },
            color: 'rgba(255, 0, 0, 0.8)',
            fontSize: 12,
            fontWeight: 'bold'
          },
          data: markAreas
        };
      }

      // Add markLine for operations (only for the first series to avoid duplicates)
      if (index === 0 && markLines.length > 0) {
        enhancedSeries.markLine = {
          silent: true,
          data: markLines
        };
      }

      return enhancedSeries;
    });

    // Enhance configuration for full screen mode
    if (isFullScreen) {
      // Override all grid settings for full screen optimization
      enhancedOption.grid = {
        left: '1%',
        right: '2%',
        top: '3%',
        bottom: '5%',
        containLabel: true
      };

      // Adjust legend position for full screen
      if (enhancedOption.legend) {
        enhancedOption.legend.top = '0%';
        enhancedOption.legend.itemGap = 8;
        enhancedOption.legend.textStyle = { fontSize: 11 };
      } else {
        enhancedOption.legend = {
          show: false
        };
      }

      enhancedOption.title = enhancedOption.title ? {
        ...enhancedOption.title,
        top: '0%',
        textStyle: {
          ...(enhancedOption.title.textStyle || {}),
          fontSize: 16
        }
      } : undefined;

      enhancedOption.animation = false;
      
      // Override any existing sizing constraints
      if (enhancedOption.series) {
        enhancedOption.series = enhancedOption.series.map((series: any) => ({
          ...series,
          sampling: 'lttb',
        }));
      }
    }

    return enhancedOption;
  };

  // ECharts event handlers
  const handleEChartsEvents = {
    brushEnd: (params: any) => {
      // Only trigger popup when brush selection is complete
      if (params.areas && params.areas.length > 0) {
        const area = params.areas[0];
        const coordRange = area.coordRange;

        if (coordRange && coordRange.length >= 2) {
          const [xRange, yRange] = coordRange;
          const selectedTab = columnTabs[parseInt(activeKey)]?.name;

          const selectionData = {
            x1: xRange[0],
            x2: xRange[1],
            y1: yRange[0],
            y2: yRange[1],
            selectedTab: selectedTab,
            xRange,
            yRange
          };

          // Show popup at center of the panel after selection is complete
          setTimeout(() => {
            setSelectionPopup({
              visible: true,
              x: 0,
              y: 0,
              selectionData
            });
          }, 100); // Small delay to ensure selection is complete
        }
      }
    },

    dataZoom: (params: any) => {
      if (params.batch && params.batch.length > 0) {
        const zoomInfo = params.batch[0];
        if (zoomInfo.startValue !== undefined && zoomInfo.endValue !== undefined) {
          const selectedTab = columnTabs[parseInt(activeKey)]?.name;
          
          console.log('@@@ TIMESERIES ZOOM:', {
            originalStart: zoomInfo.startValue,
            originalEnd: zoomInfo.endValue,
            startDate: new Date(zoomInfo.startValue).toLocaleString('en-CA', { 
              year: 'numeric', 
              month: '2-digit', 
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }).replace(',', ''),
            endDate: new Date(zoomInfo.endValue).toLocaleString('en-CA', { 
              year: 'numeric', 
              month: '2-digit', 
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }).replace(',', '')
          });
          
          const data = {
            x1: zoomInfo.startValue,
            x2: zoomInfo.endValue,
            selectedTab
          };

          // Call parent's zoom handler with original timestamps
          onZoomSelection?.('date', zoomInfo.startValue, zoomInfo.endValue, 'time-series-panel', data);
        }
      }
    }
  };

  // Get annotations from Redux store - used in other parts of the component
  useSelector((state: any) => state.annotations);

  // Get individual column options from backend
  const columnOptions = useMemo(() => {

    // Use filtered data or original data based on toggle state
    const dataToProcess = showFullData ? data : (filteredData || data);


    if (!dataToProcess || isLoading) {
      return {};
    }

    // Check if we have backend-processed column options
    if (dataToProcess.columnOptions) {
      return dataToProcess.columnOptions;
    }
    return {};
  }, [data, filteredData, isLoading, showFullData]);

  // Extract column names and create plot data for compatibility
  const plotData = useMemo(() => {
    if (!columnOptions || Object.keys(columnOptions).length === 0) return [];

    // Convert backend column options to our internal format for compatibility with existing tab logic
    return Object.entries(columnOptions).map(([columnName, option]: [string, any]) => {
      const series = option.series?.[0]; // Each column option has one series
      return {
        name: columnName,
        data: series?.data || [],
        x: series?.data?.map((point: any) => point[0]) || [],
        y: series?.data?.map((point: any) => point[1]) || [],
        type: 'scatter',
        mode: 'lines+markers',
        echartsReady: true,
        echartsOption: option // Store the complete ECharts option for this column
      };
    });
  }, [columnOptions]);

  // Apply only column selection, not date filtering
  const filteredPlotData = useMemo(() => {
    let filtered = [...plotData];


    // Apply column selection if any
    if (selectedColumns.indices.length > 0 && selectedColumns.headers.length > 0) {
      // Filter series by selected column headers
      filtered = filtered.filter(series =>
        selectedColumns.headers.includes(series.name)
      );
    }

    return filtered;
  }, [plotData, selectedColumns]);

  // Create stacked ECharts option from individual column options
  const createStackedOption = useMemo(() => {
    if (!filteredPlotData || filteredPlotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ];

    return {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: filteredPlotData.map(s => s.name),
        top: isFullScreen ? '0%' : 30,
        type: 'scroll',
        ...(isFullScreen ? { 
          itemGap: 8,
          textStyle: { fontSize: 11 }
        } : {})
      },
      grid: filteredPlotData.map((_, index) => {
        // Optimize grid spacing for full screen mode
        const leftMargin = isFullScreen ? '1%' : '10px';
        const rightMargin = isFullScreen ? '2%' : '10px';
        const topBase = isFullScreen ? 2 : 15;
        const heightBase = isFullScreen ? 96 : 75;
        const bottomMargin = isFullScreen ? 2 : 10;

        return {
          left: leftMargin,
          right: rightMargin,
          top: `${topBase + index * (heightBase / filteredPlotData.length)}%`,
          height: `${(heightBase - bottomMargin) / filteredPlotData.length}%`,
          containLabel: true
        };
      }),
      xAxis: filteredPlotData.map((_, index) => ({
        type: 'time',
        gridIndex: index,
        boundaryGap: false,
        axisLine: {
          onZero: false
        },
        show: index === filteredPlotData.length - 1 // Only show x-axis on bottom chart
      })),
      yAxis: filteredPlotData.map((s, index) => ({
        type: 'value',
        gridIndex: index,
        name: s.name,
        nameLocation: 'middle',
        nameGap: 50,
        scale: true,
        splitArea: {
          show: true
        }
      })),
      toolbox: {
        show: false
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: filteredPlotData.map((_, i) => i),
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: filteredPlotData.map((_, i) => i),
          filterMode: 'filter'
        }
      ],
      series: filteredPlotData.map((s, index) => {
        // Get annotations and operations for this specific series
        const columnAnnotations = selectedAnnotations.find((ann: any) => ann.columnName === s.name)?.annotations?.filter((a: any) => a.selected === true && a.applyAsFilter !== true) || [];
        const columnOperations = operationsState?.operations?.find((op: any) => op.columnName === s.name)?.operations?.filter((o: any) => o.selected === true) || [];

        // Create markArea for annotations
        const markAreas = columnAnnotations.map((annotation: any) => [
          {
            xAxis: new Date(annotation.x0).getTime(),
            yAxis: annotation.y0
          },
          {
            xAxis: new Date(annotation.x1).getTime(),
            yAxis: annotation.y1
          }
        ]);

        // Create markLine for operations
        const markLines: any[] = [];
        columnOperations.forEach((operation: any, opIndex: number) => {
          markLines.push({
            yAxis: operation.y0,
            lineStyle: {
              color: 'rgba(0, 128, 0, 0.7)',
              width: 2,
              type: 'dashed'
            },
            label: { show: false }
          });
          markLines.push({
            yAxis: operation.y1,
            lineStyle: {
              color: 'rgba(0, 128, 0, 0.7)',
              width: 2,
              type: 'dashed'
            },
            label: {
              show: true,
              position: 'start',
              formatter: operation?.label?.text || `Operation ${opIndex + 1}`,
              color: 'rgba(0, 128, 0, 0.8)',
              fontSize: 12,
              fontWeight: 'bold',
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
              borderColor: 'rgba(0, 128, 0, 0.7)',
              borderWidth: 1,
              borderRadius: 3,
              padding: [2, 4]
            }
          });
        });

        return {
          name: s.name,
          type: 'line',
          xAxisIndex: index,
          yAxisIndex: index,
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: colors[index % colors.length]
          },
          itemStyle: {
            color: colors[index % colors.length]
          },
          emphasis: {
            focus: 'series'
          },
          markArea: markAreas.length > 0 ? {
            silent: true,
            itemStyle: {
              color: 'rgba(255, 0, 0, 0.1)',
              borderColor: 'rgba(255, 0, 0, 0.7)',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'inside',
              formatter: function(params: any) {
                const annotation = columnAnnotations[params.dataIndex];
                return annotation?.label?.text || 'Anomaly';
              },
              color: 'rgba(255, 0, 0, 0.8)',
              fontSize: 12,
              fontWeight: 'bold'
            },
            data: markAreas
          } : undefined,
          markLine: markLines.length > 0 ? {
            silent: true,
            data: markLines
          } : undefined
        };
      })
    };
  }, [filteredPlotData, selectedAnnotations, operationsState, isFullScreen]);

  // Group data by column for tabs - defined early to avoid circular dependencies
  const columnTabs = useMemo(() => {
    // If no plot data, return empty array
    if (!filteredPlotData || filteredPlotData.length === 0) {
      console.log('columnTabs - No filtered plot data available');
      return [];
    }

    console.log('columnTabs - Creating tabs for columns:',
      filteredPlotData.map(series => series.name));

    // Check if we have backend extremes data
    const dataToUse = filteredData || data;
    const backendExtremes = dataToUse?.echartsData?.extremes;

    // Create a tab for each column
    return filteredPlotData.map(series => {
      let yRange = undefined;

      // Try to use backend extremes first
      if (backendExtremes && backendExtremes[series.name]) {
        yRange = [backendExtremes[series.name].min, backendExtremes[series.name].max];
      } else if (series.y && series.y.length > 0) {
        // Fallback to frontend calculation
        const validYValues = series.y.filter((val: any) => !isNaN(parseFloat(val)));
        if (validYValues.length > 0) {
          const minY = Math.min(...validYValues.map((val: any) => parseFloat(val)));
          const maxY = Math.max(...validYValues.map((val: any) => parseFloat(val)));
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log(`columnTabs - Flat line detected for ${series.name}, using padding:`, flatLinePadding);
            yRange = [minY - flatLinePadding, maxY + flatLinePadding];
          } else {
            // Normal case with range
            const padding = range * 0.2;
            const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;

            console.log(`columnTabs - Y-axis range for ${series.name}:`, {
              min: minY,
              max: maxY,
              range: range,
              padding: effectivePadding,
              paddedMin: minY - effectivePadding,
              paddedMax: maxY + effectivePadding
            });

            yRange = [minY - effectivePadding, maxY + effectivePadding];
          }
        }
      }

      return {
        name: series.name,
        data: [series],
        yRange: yRange
      };
    });
  }, [filteredPlotData, data, filteredData]);

  // Helper function to check if there are any active filters for the current column
  const checkForActiveFilters = useCallback((columnName: string) => {
    // Check annotations for active filters
    const annotationGroup = selectedAnnotations.find((group: any) => group.columnName === columnName);
    const hasActiveAnnotationFilters = annotationGroup?.annotations?.some((annotation: any) => annotation.applyAsFilter === true);

    // Check operations for active filters
    const operationGroup = operationsState?.operations?.find((group: any) => group.columnName === columnName);
    const hasActiveOperationFilters = operationGroup?.operations?.some((operation: any) => operation.applyAsFilter === true);

    const result = hasActiveAnnotationFilters || hasActiveOperationFilters;

    console.log(`checkForActiveFilters - Column: "${columnName}"`, {
      hasActiveAnnotationFilters,
      hasActiveOperationFilters,
      result,
      annotationGroup: annotationGroup?.annotations?.map((a: any) => ({ shapeId: a.shapeId, applyAsFilter: a.applyAsFilter })),
      operationGroup: operationGroup?.operations?.map((o: any) => ({ operationId: o.operationId, applyAsFilter: o.applyAsFilter }))
    });

    return result;
  }, [selectedAnnotations, operationsState]);

  const fetchAnnotations = useCallback(async (forceRefetch: boolean = false) => {
    try {
      // Check if we have valid columnTabs and activeKey
      if (!columnTabs || columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
        console.log('fetchAnnotations - No valid columnTabs or activeKey, skipping API call');
        return;
      }

      const currentColumnName = columnTabs[parseInt(activeKey)].name;

      // Prevent unnecessary API calls for the same column unless forced
      if (!forceRefetch && lastFetchedColumnRef.current === currentColumnName) {
        console.log(`fetchAnnotations - Already fetched data for column "${currentColumnName}", skipping API call`);
        return;
      }

      // CRITICAL FIX: Check if any annotations or operations have applyAsFilter=true
      // If so, skip the API call to prevent overwriting the filter state
      const hasActiveFilters = checkForActiveFilters(currentColumnName);
      if (hasActiveFilters && !forceRefetch) {
        console.log(`fetchAnnotations - Skipping API call for column "${currentColumnName}" because filters are currently applied`);
        return;
      }

      console.log(`fetchAnnotations - Fetching config for column: "${currentColumnName}"`);

      const dataToSend = {
        columnName: currentColumnName,
        systems: JSON.stringify(selectSystems[0]?.systems || []),
      };

      const response = await postRequest('/file/get-json-file', dataToSend);

      if (response?.data?.data) {
        const { anomaly, operation } = response.data.data;

        dispatch(
          setAnnotations([
            {
              columnName: currentColumnName,
              annotations: anomaly,
            },
          ])
        );
        dispatch(
          setOperations([
            {
              columnName: currentColumnName,
              operations: operation,
            },
          ])
        );

        // Update the ref to track the last fetched column
        lastFetchedColumnRef.current = currentColumnName;
        console.log(`fetchAnnotations - Successfully fetched and cached data for column: "${currentColumnName}"`);
      }
    } catch (error) {
      console.error('Error fetching annotations:', error);
    }
  }, [columnTabs, activeKey, selectSystems, dispatch, checkForActiveFilters]);

  // Only fetch annotations when activeKey changes (actual tab change)
  useEffect(() => {
    if (columnTabs.length > 0 && columnTabs[parseInt(activeKey)]) {
      fetchAnnotations();
    }
  }, [activeKey, fetchAnnotations]);

  // Set initial zoom range based on dateFilter and update when data changes
  useEffect(() => {
    // Only proceed if we have columnTabs and activeKey is valid
    if (columnTabs.length === 0 || !columnTabs[parseInt(activeKey)]) {
      return;
    }

    const selectedTab = columnTabs[parseInt(activeKey)];

    // Get extremes from filtered data if available
    const dataToUse = filteredData && filteredData.length > 0 ? filteredData : data;
    const extremes = getExtremes(dataToUse, selectedTab.name);

    if (!extremes) return;

    // If dateFilter is set, use it for x-axis range
    const xRange = dateFilter.startDate && dateFilter.endDate ?
      [dateFilter.startDate, dateFilter.endDate] :
      [extremes.DateTime.min, extremes.DateTime.max];

    console.log('Setting zoom range based on data and filters:', {
      xRange,
      yRange: [extremes[selectedTab.name].min, extremes[selectedTab.name].max]
    });

    setLayout((prev: any) => ({
      ...prev,
      xaxis: {
        ...(prev?.xaxis || {}),
        range: xRange,
        fixedrange: false, // Allow x-axis zooming
      },
      yaxis: {
        ...(prev?.yaxis || {}),
        // Set the range with our calculated values (with padding)
        range: [
          extremes[selectedTab.name].min,
          extremes[selectedTab.name].max
        ],
        fixedrange: true, // Lock y-axis when zooming
        autorange: false, // Disable autorange to use our custom range
      },
      dragmode: drawMode, // Ensure the current draw mode is maintained
    }));
  }, [filteredData, data, dateFilter, drawMode, activeKey, columnTabs]);

  // We'll remove the toggle functionality as requested

  // ECharts layout state (keeping for compatibility with existing useEffect hooks)
  const [layout1, setLayout] = useState<any>({});




  // Popup action handlers
  const handlePopupAction = (action: string) => {
    const { selectionData } = selectionPopup;
    if (!selectionData) return;

    const { x1, x2, y1, y2, selectedTab } = selectionData;

    switch (action) {
      case 'zoom':
        console.log('@@@ POPUP ZOOM:', {
          originalStart: x1,
          originalEnd: x2,
          startDate: new Date(x1).toLocaleString('en-CA', { 
            year: 'numeric', 
            month: '2-digit', 
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(',', ''),
          endDate: new Date(x2).toLocaleString('en-CA', { 
            year: 'numeric', 
            month: '2-digit', 
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).replace(',', '')
        });
        
        // Call parent's zoom handler for ECharts
        if (onZoomSelection) {
          onZoomSelection('date', x1, x2, 'time-series-panel', selectionData);
        }
        break;

      case 'anomaly':
        // Create annotation for anomaly (maintaining compatibility with existing format)
        const anomalyAnnotation = {
          type: 'rect',
          xref: 'x',
          yref: 'y',
          x0: x1,
          y0: y1,
          x1: x2,
          y1: y2,
          fillcolor: 'rgba(255, 0, 0, 0.1)',
          line: {
            color: 'rgba(255, 0, 0, 0.7)',
            width: 2,
            dash: 'solid'
          },
          shapeId: `anomaly-${Date.now()}`,
          label: {
            text: 'Anomaly'
          },
          selected: true,
          applyAsFilter: false
        };

        dispatch(addAnnotation({
          columnName: selectedTab,
          annotation: anomalyAnnotation
        }));
        break;

      case 'operation':
        // Create operation range (maintaining compatibility with existing format)
        const operationRange = {
          type: 'line',
          xref: 'paper',
          yref: 'y',
          y0: y1,
          y1: y2,
          line: {
            color: 'rgba(0, 128, 0, 0.7)',
            width: 2,
            dash: 'dash'
          },
          layer: 'below',
          operationId: `operation-${Date.now()}`,
          label: {
            text: `Operation Range ${y1.toFixed(2)} - ${y2.toFixed(2)}`
          },
          selected: true
        };

        dispatch(addOperation({
          columnName: selectedTab,
          operation: operationRange
        }));
        break;
    }

    // Hide popup after action
    setSelectionPopup(prev => ({ ...prev, visible: false }));
  };

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectionPopup.visible) {
        const target = event.target as Element;
        if (!target.closest('.selection-popup')) {
          setSelectionPopup(prev => ({ ...prev, visible: false }));
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [selectionPopup.visible]);




  // ECharts chart update trigger - force re-render when annotations/operations change
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);

  useEffect(() => {
    // Trigger chart re-render when annotations or operations change
    setChartUpdateTrigger(prev => prev + 1);
  }, [selectedAnnotations, operationsState, activeKey]);


  // We don't need activeTab anymore as we're using columnTabs[parseInt(activeKey)] directly

  const handleTabChange = (key: string) => {
    setActiveKey(key);

    // Ensure we update the active column name in both slices
    if (columnTabs.length > 0 && columnTabs[parseInt(key)]) {
      const tab = columnTabs[parseInt(key)];
      const columnName = tab.name;
      dispatch(setActiveColumnName(columnName));
      dispatch(setOperationActiveColumnName(columnName));

      // Note: fetchAnnotations will be called automatically by useEffect when activeKey changes

      // Log detailed information about the data and ranges for debugging
      console.log('TAB CHANGE - Column Name:', columnName);
      console.log('TAB CHANGE - Tab Data:', tab);

      // Log filtered data for this column
      const filteredDataForColumn = filteredData && filteredData.length > 0 ?
        filteredData.map((row: any) => ({
          DateTime: row.DateTime,
          [columnName]: row[columnName]
        })) : [];

      console.log('TAB CHANGE - Filtered Data Sample (first 5 rows):',
        filteredDataForColumn.slice(0, 5));
      console.log('TAB CHANGE - Filtered Data Length:',
        filteredDataForColumn.length);

      // Calculate and log min/max values for this column
      if (filteredDataForColumn.length > 0) {
        const yValues = filteredDataForColumn
          .map((row: any) => parseFloat(row[columnName]))
          .filter((val: number) => !isNaN(val));

        if (yValues.length > 0) {
          const minY = Math.min(...yValues);
          const maxY = Math.max(...yValues);
          const range = maxY - minY;

          // Special case for flat line
          if (range === 0 || range < 0.000001) {
            const flatLinePadding = Math.max(1, Math.abs(maxY) * 0.5);
            console.log('TAB CHANGE - Flat line detected, using special padding:', flatLinePadding);

            // Update layout with flat line padding
            setLayout((prev: any) => ({
              ...prev,
              yaxis: {
                ...prev.yaxis,
                range: [minY - flatLinePadding, maxY + flatLinePadding],
                autorange: false
              }
            }));

            return; // Skip the rest of the function
          }

          const padding = range * 0.2;
          const effectivePadding = range < 0.01 ? Math.max(0.5, range) : padding;
          const paddedMin = minY - effectivePadding;
          const paddedMax = maxY + effectivePadding;

          console.log('TAB CHANGE - Y-Axis Values:', {
            rawMin: minY,
            rawMax: maxY,
            range: range,
            padding: effectivePadding,
            paddedMin: paddedMin,
            paddedMax: paddedMax
          });

          // Directly update the layout with the calculated range
          setLayout((prev: any) => ({
            ...prev,
            yaxis: {
              ...prev.yaxis,
              range: [paddedMin, paddedMax],
              autorange: false
            }
          }));

          // Log current layout settings after update
          setTimeout(() => {
            console.log('TAB CHANGE - Updated Layout:', {
              xaxisRange: layout1.xaxis?.range,
              yaxisRange: layout1.yaxis?.range,
              yaxisAutorange: layout1.yaxis?.autorange
            });
          }, 100);

          return; // Skip the rest of the function
        }
      }

      // If we have a pre-calculated yRange in the tab, use it
      if (tab.yRange) {
        console.log('TAB CHANGE - Using pre-calculated yRange:', tab.yRange);

        setLayout((prev: any) => ({
          ...prev,
          yaxis: {
            ...prev.yaxis,
            range: tab.yRange,
            autorange: false
          }
        }));

        return; // Skip the rest of the function
      }

      // Log current layout settings
      console.log('TAB CHANGE - Current Layout:', {
        xaxisRange: layout1.xaxis?.range,
        yaxisRange: layout1.yaxis?.range,
        yaxisAutorange: layout1.yaxis?.autorange
      });
    }

    // Use setTimeout to ensure the tab change is processed before updating the layout
    // Only run this if we didn't already set the layout above
    setTimeout(() => {
      setZoomLayout();

      // Log the layout after update
      setTimeout(() => {
        console.log('TAB CHANGE - Updated Layout (from setZoomLayout):', {
          xaxisRange: layout1.xaxis?.range,
          yaxisRange: layout1.yaxis?.range,
          yaxisAutorange: layout1.yaxis?.autorange
        });
      }, 100);
    }, 0);
  };

  // Early returns after all hooks are called
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading time series data..." />
      </div>
    );
  }

  if (!filteredPlotData || filteredPlotData.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No time series data available" />
      </div>
    );
  }

  // Determine if we should show stacked view - always show stacked view when columns are selected
  const shouldShowStackedView = selectedColumns.indices.length > 0 && filteredPlotData.length > 0;

  // Render the component

    return (
      <div className={`time-series-panel ${isFullScreen ? 'h-full' : 'h-[95%]'}`} ref={containerRef} style={{ position: 'relative' }}>
        {/* Applied Filters - Show only in full screen mode */}
        {isFullScreen && (
          <div className="px-2 pt-2 pb-1">
            <div className="flex flex-wrap items-center mb-4">
              <div className="flex-grow">
                <AppliedFilters
                  selectedColumns={selectedColumns}
                  dateFilter={dateFilter}
                  conditionalFilters={conditionalFilters || []}
                  onRemoveFilter={(filterId: string, panelId: string) => {
                    // Handle different types of filter removal
                    if (filterId === 'date-filter') {
                      // Clear date filter
                      if (onDateFilterChange) {
                        onDateFilterChange(null, null);
                      }
                    } else if (filterId === 'column-filter') {
                      // Clear column selection
                      if (onColumnSelection) {
                        onColumnSelection([], []);
                      }
                    } else {
                      // Handle conditional filters and other filter types
                      if (onRemoveFilter) {
                        onRemoveFilter(filterId, panelId);
                      }
                    }
                  }}
                  onClearAllFilters={() => {
                    // Clear all filters
                    if (onClearAllFilters) {
                      onClearAllFilters();
                    } else {
                      // Fallback: clear individual filters
                      if (onDateFilterChange) onDateFilterChange(null, null);
                      if (onColumnSelection) onColumnSelection([], []);
                    }
                  }}
                />
              </div>

              {/* Clear Filters Button */}
              <div className="flex gap-2">
                <Button
                  type="default"
                  icon={<ClearOutlined />}
                  onClick={() => {
                    if (onClearAllFilters) {
                      onClearAllFilters();
                    } else {
                      // Fallback: clear individual filters
                      if (onDateFilterChange) onDateFilterChange(null, null);
                      if (onColumnSelection) onColumnSelection([], []);
                    }
                  }}
                  style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </div>
        )}
        {
          shouldShowStackedView ? (
          <>
          <div className={`flex justify-between items-center ${isFullScreen ? 'mb-0 px-2 pt-0' : 'mb-1 px-3 pt-1'}`}>
            <h3 className="text-base font-medium">Time Series ({filteredPlotData.length})</h3>
            {/* <div className="flex items-center">
              <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                <Switch
                  checked={showFullData}
                  onChange={setShowFullData}
                  checkedChildren="Full Data"
                  unCheckedChildren="Filtered"
                  className="mr-2"
                  size="small"
                />
              </Tooltip>
            </div> */}
          </div>
        <div className={`${isFullScreen ? 'p-0 h-[calc(100%-100px)]' : 'p-2 h-[calc(100%-30px)]'} overflow-auto`}>
          <div style={{ 
            height: '100%', 
            minHeight: isFullScreen ? '100%' : `${Math.max(400, filteredPlotData.length * 150)}px` 
          }}>
            {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
              <ReactECharts
                key={`stacked-${chartUpdateTrigger}`}
                ref={echartsRef}
                option={createStackedOption}
                style={{ 
                  width: '100%', 
                  height: '100%',
                  ...(isFullScreen ? {} : { minHeight: '400px' })
                }}
                onEvents={{
                  brushEnd: handleEChartsEvents.brushEnd,
                  dataZoom: handleEChartsEvents.dataZoom
                }}
                opts={{ renderer: 'canvas' }}
              />
            ) : (
              <div style={{ 
                width: '100%', 
                height: isFullScreen ? '100%' : '400px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center' 
              }}>
                <Spin size="large" tip="Loading chart..." />
              </div>
            )}
          </div>
        </div></>) :(<>
          {!isFullScreen && (
            <div className="flex justify-between items-center mb-1 px-3 pt-0">
              {/* <h3 className="text-base font-medium">Time Series</h3> */}
              {/* <div className="flex items-center">
                <Tooltip title={showFullData ? "Showing all data" : "Showing filtered data"}>
                  <Switch
                    checked={showFullData}
                    onChange={setShowFullData}
                    checkedChildren="Full Data"
                    unCheckedChildren="Filtered"
                    className="mr-2"
                    size="small"
                  />
                </Tooltip>
              </div> */}
            </div>
          )}
                      <Tabs
              defaultActiveKey="0"
              style={{
                height: isFullScreen ? 'calc(100% - 80px)' : 'calc(100% - 40px)',
                ...(isFullScreen ? {
                  margin: 0,
                  padding: 0
                } : {})
              }}
            onChange={handleTabChange} 
            className={isFullScreen ? '[&_.ant-tabs-nav]:m-0 [&_.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full' : '[&_.ant-tabs-nav]:m-0'}
          >
          {columnTabs.map((tab, index) => (
            <Tabs.TabPane tab={tab.name} key={index.toString()}>
              <div className={`w-full ${isFullScreen ? 'h-full' : 'h-full'}`} style={{ 
                ...(isFullScreen ? { 
                  padding: 0,
                  margin: 0,
                  height: '100%'
                } : {})
              }}>
                {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
                  <ReactECharts
                    key={`tab-${index}-${chartUpdateTrigger}`}
                    ref={echartsRef}
                    option={addAnnotationsToOption(tab.data[0]?.echartsOption || {}, tab.name)}
                    style={{ 
                      width: '100%', 
                      height: '100%',
                      ...(isFullScreen ? { 
                        minHeight: '100%',
                        maxHeight: '100%'
                      } : { minHeight: '300px' })
                    }}
                    onEvents={{
                      brushEnd: handleEChartsEvents.brushEnd,
                      dataZoom: handleEChartsEvents.dataZoom
                    }}
                    opts={{ renderer: 'canvas' }}
                  />
                ) : (
                  <div style={{ 
                    width: '100%', 
                    height: isFullScreen ? '100%' : '300px', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center' 
                  }}>
                    <Spin size="large" tip="Loading chart..." />
                  </div>
                )}
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs></>)
        }

        {/* Selection Popup */}
        {selectionPopup.visible && (
          <div
            className="selection-popup"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)', // Perfect centering
              zIndex: 100, // Lower z-index to not overlap other panels
              backgroundColor: 'white',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
              padding: '8px 0',
              minWidth: '220px',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
            }}
          >
            <div style={{
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: '500',
              color: '#262626',
              borderBottom: '1px solid #f0f0f0',
              marginBottom: '4px'
            }}>
              Selection Actions
            </div>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div
                onClick={() => handlePopupAction('zoom')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>🔍</span>
                Zoom to Selection
              </div>
              <div
                onClick={() => handlePopupAction('anomaly')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>📊</span>
                Mark as Anomaly
              </div>
              <div
                onClick={() => handlePopupAction('operation')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#262626',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>⚙️</span>
                Set Operation Range
              </div>
              <div style={{ height: '1px', backgroundColor: '#f0f0f0', margin: '4px 0' }} />
              <div
                onClick={() => setSelectionPopup(prev => ({ ...prev, visible: false }))}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: '#8c8c8c',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f5f5f5'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
              >
                <span style={{ marginRight: '8px', fontSize: '16px' }}>✕</span>
                Cancel
              </div>
            </div>
          </div>
        )}
      </div>
    );
  // } else {
  //   // Original tab-based view
  //   return (
  //     <div className="time-series-panel h-full">
  //       <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
  //         {columnTabs.map((tab, index) => (
  //           <Tabs.TabPane tab={tab.name} key={index.toString()}>
  //             <div className="p-4 h-full">
  //               <Plot
  //                 data={tab.data as any}
  //                 layout={{
  //                   ...layout,
  //                   title: `Time Series: ${tab.name}`,
  //                   showlegend: tab.data.length > 1
  //                 } as any}
  //                 config={config as any}
  //                 style={{ width: '100%', height: 'calc(100% - 20px)' }}
  //                 useResizeHandler={true}
  //               />
  //             </div>
  //           </Tabs.TabPane>
  //         ))}
  //       </Tabs>
  //     </div>
  //   );
  // }
};

export default TimeSeriesPanel;
