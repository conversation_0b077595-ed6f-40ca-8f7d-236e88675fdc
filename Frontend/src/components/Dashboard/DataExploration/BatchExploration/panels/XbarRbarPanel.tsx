import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../../types';
import { PanelFilter } from '../../FilterTypes';
import { Spin, Empty, Card, Typography, Tabs } from 'antd';
import ReactECharts from 'echarts-for-react';

const { Title, Text } = Typography;

interface XbarRbarPanelProps {
  data: any;
  filteredData?: any;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
  isFullScreen?: boolean;
}

const XbarRbarPanel: React.FC<XbarRbarPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  onZoomSelection,
  isFullScreen = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerSize({ width: clientWidth, height: clientHeight });
        setTimeout(() => setIsChartReady(true), 100);
      }
    };

    updateSize();
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const { width, height } = entry.contentRect;
        if (width > 0 && height > 0) {
          setContainerSize({ width, height });
          setTimeout(() => setIsChartReady(true), 100);
        }
      }
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => resizeObserver.disconnect();
  }, [isFullScreen]);

  // Process X̄-R chart data from backend
  const xbarRbarData = useMemo(() => {
    if (!filteredData || !filteredData.columnOptions) return {};
    return filteredData.columnOptions;
  }, [filteredData]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="xbar-rbar-panel h-full flex items-center justify-center">
        <Spin size="large" tip="Loading X̄-R chart data..." />
      </div>
    );
  }

  // Handle empty data state
  if (!data && !filteredData) {
    return (
      <div className="xbar-rbar-panel h-full flex items-center justify-center">
        <Empty description="No data available for X̄-R chart" />
      </div>
    );
  }

  return (
    <div className="xbar-rbar-panel h-full w-full" ref={containerRef}>
      {Object.keys(xbarRbarData).length > 0 ? (
        <Tabs
          defaultActiveKey="0"
          className="h-full [&_.ant-tabs-nav]:m-0 [&_.ant-tabs-content]:h-full [&_.ant-tabs-tabpane]:h-full"
        >
          {Object.keys(xbarRbarData).map((columnName, index) => (
            <Tabs.TabPane tab={columnName} key={index.toString()}>
              <div className="w-full h-full" style={{ padding: 0, margin: 0 }}>
                {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
                  <ReactECharts
                    key={`xbar-rbar-${index}`}
                    option={xbarRbarData[columnName]}
                    style={{
                      width: '100%',
                      height: '100%'
                    }}
                    opts={{ renderer: 'canvas' }}
                  />
                ) : (
                  <div style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Spin size="large" tip="Loading chart..." />
                  </div>
                )}
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs>
      ) : (
        <div className="h-full flex items-center justify-center">
          <Empty description="No X̄-R chart data available" />
        </div>
      )}
    </div>
  );
};

export default XbarRbarPanel;
