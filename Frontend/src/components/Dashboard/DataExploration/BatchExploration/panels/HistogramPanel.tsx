import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../../types';
import { PanelFilter } from '../../FilterTypes';
import { Spin, Empty, Tabs } from 'antd';
import Plot from 'react-plotly.js';

interface HistogramPanelProps {
  data: any;
  filteredData?: any;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
}

interface ColumnHistogram {
  name: string;
  values?: number[];
  bins?: {
    x: number;
    y: number;
  }[];
  min: number;
  max: number;
  // Backend data
  plotlyData?: any[];
  plotlyLayout?: any;
  statistics?: any;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const HistogramPanel: React.FC<HistogramPanelProps> = ({
  data,
  filteredData,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectedColumns = { indices: [], headers: [] },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  dateFilter = { startDate: null, endDate: null }, // Keep for interface compatibility
  isLoading = false,
  panelFilters = [],
  onZoomSelection
}) => {

  const containerRef = useRef<HTMLDivElement>(null);
  const [plotHeight, setPlotHeight] = useState(250); // default fallback

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        setPlotHeight(Math.floor(height * 0.65));
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, []);




  // Get column histograms from backend-processed data
  const columnHistograms = useMemo(() => {

    if (!data || isLoading) {
      return [];
    }

    // Check if data has the expected backend structure
    if (!data.columnOptions || typeof data.columnOptions !== 'object') {
      return [];
    }

    const columnOptionsKeys = Object.keys(data.columnOptions);

    // Backend already calculated all histograms, just convert to our format
    const histograms = Object.entries(data.columnOptions).map(([columnName, columnData]: [string, any]) => {

      if (!columnData?.plotlyData || !columnData?.plotlyLayout || !columnData?.statistics) {
        return null;
      }

      // Convert backend format to our interface format for compatibility
      const histogram = {
        name: columnName,
        values: [], 
        bins: [],
        min: parseFloat(columnData.statistics.min),
        max: parseFloat(columnData.statistics.max),
        // Store backend data for direct Plotly usage
        plotlyData: columnData.plotlyData,
        plotlyLayout: columnData.plotlyLayout,
        statistics: columnData.statistics
      };

      return histogram;
    }).filter(Boolean);

    return histograms;
  }, [data, isLoading]);

  // Configure plot options
  const config = {
    responsive: true,
    displayModeBar: false,
    doubleClick: 'reset',
    displaylogo: false
  };

  // Handle zoom selection events
  const handleZoomSelection = (eventData: any) => {
    if (!onZoomSelection || !eventData) return;

    // Extract the selected range from the event data
    let xRange;
    if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {
      xRange = [eventData['xaxis.range[0]'], eventData['xaxis.range[1]']];
    } else if (eventData['xaxis.range']) {
      xRange = eventData['xaxis.range'];
    }

    if (!xRange || xRange.length !== 2) return;

    // Get the column name from the active tab
    const activeTabIndex = parseInt(document.querySelector('.ant-tabs-tabpane-active')?.getAttribute('data-node-key') || '0');
    const column = columnHistograms[activeTabIndex]?.name;
    if (!column) return;

    const min = xRange[0];
    const max = xRange[1];

    // Check if this is a datetime column - exact match only (case insensitive)
    const isDateColumn = column === 'DateTime';

    if (isDateColumn) {
      // For date columns, use a fixed column name
      onZoomSelection('date', min, max, 'histogram-panel');
    } else {
      // For regular numeric columns, use the actual column name
      onZoomSelection(column, min, max, 'histogram-panel');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading histogram data..." />
      </div>
    );
  }

  if (!columnHistograms || columnHistograms.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No histogram data available" />
      </div>
    );
  }

  return (
    <div className="histogram-panel h-full" ref={containerRef}>
      <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
        {columnHistograms.map((column, index) => {
          if (!column) return null;
          return (
            <Tabs.TabPane tab={column.name} key={index.toString()}>
              <div className="p-4 h-full flex flex-col">
                <div className="flex-1">
                  <Plot
                  data={column.plotlyData || [] as any}
                  layout={{
                    ...column.plotlyLayout,
                    title: false,
                    autosize: true,
                    height: plotHeight,
                    margin: { l: 40, r: 40, b: 40, t: 10, pad: 0 },
                    xaxis: {
                      ...column.plotlyLayout?.xaxis,
                      showgrid: true,
                      zeroline: false,
                      type: 'linear',
                      automargin: true,
                      fixedrange: false,
                      linecolor: '#000000',
                      gridcolor: '#cccccc',
                      tickcolor: '#000000',
                      tickfont: { color: '#000000' },
                      titlefont: { color: '#000000' }
                    },
                    yaxis: {
                      ...column.plotlyLayout?.yaxis,
                      showgrid: true,
                      zeroline: false,
                      automargin: true,
                      fixedrange: false,
                      linecolor: '#000000',
                      gridcolor: '#cccccc',
                      tickcolor: '#000000',
                      tickfont: { color: '#000000' },
                      titlefont: { color: '#000000' }
                    },
                    bargap: 0,
                    bargroupgap: 0,
                    width: undefined,
                    showlegend: false
                  } as any}
                  config={{
                    ...config,
                    responsive: true
                  } as any}
                  onRelayout={handleZoomSelection}
                  style={{ width: '100%', height: '100%' }}
                  useResizeHandler={true}
                />
                </div>
                <div className="flex justify-between text-sm mt-4">
                  {column.statistics ? (
                    <>
                      <span><strong>Min:</strong> {column.statistics.min}</span>
                      <span><strong>Max:</strong> {column.statistics.max}</span>
                    </>
                  ) : (
                    <>
                      <span><strong>Min:</strong> {column.min.toFixed(2)}</span>
                      <span><strong>Max:</strong> {column.max.toFixed(2)}</span>
                    </>
                  )}
                </div>
              </div>
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </div>
  );
};

export default HistogramPanel;
