import React, { useMemo, useState, useEffect } from 'react';
import { ColumnSelection, DateFilter } from '../../types';
import { Spin, Table, Empty, Typography, Checkbox, message } from 'antd';

interface OverviewPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;

  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
}

interface ColumnStatistics {
  name: string;
  mean: string;
  min: string;
  max: string;
  stdDev: string;
  missingValues: string;
  distinctValues: string;
}

const OverviewPanel: React.FC<OverviewPanelProps> = ({
  data,
  filteredData,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  onColumnSelection,
  onDateFilterChange
}) => {
  // Track selected columns internally
  const [internalSelectedColumns, setInternalSelectedColumns] = useState<string[]>([]);

  // Initialize internal selected columns from props
  useEffect(() => {
    // If selectedColumns is empty, clear internal selection
    if (selectedColumns.indices.length === 0) {
      setInternalSelectedColumns([]);
      return;
    }

    // With backend processing, use selectedColumns.headers directly if available
    if (selectedColumns.headers.length > 0) {
      setInternalSelectedColumns(selectedColumns.headers);
    } else if (data?.columnStatistics) {
      // Fallback: map indices to column names from backend statistics
      const columnNames = data.columnStatistics.map((stat: any) => stat.name);
      const selectedNames = selectedColumns.indices
        .map(idx => columnNames[idx])
        .filter(Boolean);
      setInternalSelectedColumns(selectedNames);
    }
  }, [data, selectedColumns]);

  // Handle column selection via checkbox
  const handleColumnSelection = (record: ColumnStatistics) => {

    // With backend processing, get column names from statistics
    const allColumnNames = data?.columnStatistics?.map((stat: any) => stat.name) || [];

    // Find the index of this column in the statistics array
    const columnIndex = allColumnNames.findIndex((name: any) => name === record.name);
    if (columnIndex === -1) {
      return;
    }

    // Toggle selection
    let newSelectedColumns: string[];
    if (internalSelectedColumns.includes(record.name)) {
      // Remove from selection
      newSelectedColumns = internalSelectedColumns.filter(name => name !== record.name);
    } else {
      // Add to selection - Check if we already have 4 columns selected
      if (internalSelectedColumns.length >= 4) {
        message.warning("Only 4 columns are allowed to be selected");
        return;
      }
      newSelectedColumns = [...internalSelectedColumns, record.name];
    }

    // Update internal state
    setInternalSelectedColumns(newSelectedColumns);

    // Notify parent component
    if (onColumnSelection) {
      // Map selected column names back to indices
      const selectedIndices = newSelectedColumns
        .map(name => allColumnNames.findIndex((colName: any) => colName === name))
        .filter(idx => idx !== -1);
      onColumnSelection(selectedIndices, newSelectedColumns);
    }
  };
  // Get column statistics from backend-processed data
  const columnStatistics = useMemo(() => {

    if (!data || isLoading) {
      return [];
    }

    // Check if data has the expected backend structure
    if (!data.columnStatistics || !Array.isArray(data.columnStatistics)) {
      return [];
    }

    // Backend already calculated all statistics, just return them
    // The backend excludes DateTime columns and handles all the complex calculations
    return data.columnStatistics.map((stat: any) => ({
      name: stat.name,
      mean: stat.mean,
      min: stat.min,
      max: stat.max,
      stdDev: stat.stdDev,
      missingValues: stat.missingValues,
      distinctValues: stat.distinctValues
    }));
  }, [data, isLoading]);

  // Define table columns
  const tableColumns = [
    {
      title: 'Select',
      dataIndex: 'select',
      key: 'select',
      width: 70,
      render: (_: any, record: ColumnStatistics) => (
        <Checkbox
          checked={internalSelectedColumns.includes(record.name)}
          onChange={() => handleColumnSelection(record)}
        />
      ),
    },
    {
      title: 'Column Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => a.name.localeCompare(b.name),
    },
    {
      title: 'Mean',
      dataIndex: 'mean',
      key: 'mean',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.mean);
        const bVal = parseFloat(b.mean);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Min',
      dataIndex: 'min',
      key: 'min',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.min);
        const bVal = parseFloat(b.min);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Max',
      dataIndex: 'max',
      key: 'max',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.max);
        const bVal = parseFloat(b.max);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Std Dev',
      dataIndex: 'stdDev',
      key: 'stdDev',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => {
        const aVal = parseFloat(a.stdDev);
        const bVal = parseFloat(b.stdDev);
        return isNaN(aVal) || isNaN(bVal) ? 0 : aVal - bVal;
      },
    },
    {
      title: 'Missing Values',
      dataIndex: 'missingValues',
      key: 'missingValues',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => parseInt(a.missingValues) - parseInt(b.missingValues),
    },
    {
      title: 'Distinct Values',
      dataIndex: 'distinctValues',
      key: 'distinctValues',
      sorter: (a: ColumnStatistics, b: ColumnStatistics) => parseInt(a.distinctValues) - parseInt(b.distinctValues),
    },
  ];

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Calculating statistics..." />
      </div>
    );
  }

  if (!columnStatistics || columnStatistics.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No data available for statistics" />
      </div>
    );
  }

  return (
    <div className="overview-panel h-full p-4">
      {/* <Typography.Title level={4} style={{ marginBottom: 16 }}>Data Overview</Typography.Title> */}
      <Table
        dataSource={columnStatistics}
        columns={tableColumns}
        rowKey="name"
        pagination={false}
        size="small"
        bordered
      />
    </div>
  );
};

export default OverviewPanel;
