import React, { useState, useEffect } from 'react';
import { Drawer, Form, Button, InputNumber, Select, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';

interface PanelConfigDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  panelType: string;
  currentConfiguration?: any;
  onConfigurationChange: (config: any) => void;
}

const PanelConfigDrawer: React.FC<PanelConfigDrawerProps> = ({
  isOpen,
  onClose,
  panelType,
  currentConfiguration,
  onConfigurationChange
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  // Get systems from Redux store
  const systems = useSelector((state: any) => state.systems);


  // Extract distinct target variables from systems configurations
  const getTargetVariables = () => {
    if (!systems?.systems || !Array.isArray(systems.systems)) {
      return [];
    }

    const targetVariables = new Set<string>();

    systems.systems.forEach((system: any) => {
      if (system?.config?.[0]?.configurations?.rules && Array.isArray(system.config[0].configurations.rules)) {
        system.config[0].configurations.rules.forEach((rule: any) => {
          if (rule?.field && typeof rule.field === 'string') {
            targetVariables.add(rule.field);
          }
        });
      }
    });

    const distinctVariables = Array.from(targetVariables);
    return distinctVariables;
  };

  // Get target variables for dropdown options
  const targetVariables = getTargetVariables();
  const targetVariableOptions = targetVariables.map(variable => ({
    value: variable,
    label: variable
  }));

  // Get default target variable (first one if available, null if none found)
  const defaultTargetVariable = targetVariables.length > 0 ? targetVariables[0] : null;

  // Initialize form with existing configuration
  useEffect(() => {
    if (isOpen && currentConfiguration) {
      form.setFieldsValue(currentConfiguration);
    }
  }, [isOpen, currentConfiguration, form]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // Save configuration
      onConfigurationChange(values);
      message.success('Configuration saved successfully!');
      onClose();
    } catch (error) {
      console.error('Error saving configuration:', error);
      message.error('Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };

  // Handle drawer close
  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  // Render configuration content based on panel type
  const renderConfigurationContent = () => {
    switch (panelType) {
      case 'XbarRbarPanel':
        return (
          <div>
            <h3 className="text-lg font-medium mb-4">X̄-R Chart Configuration</h3>
            <Form.Item
              name="subgroupSize"
              label="Subgroup Size"
              rules={[
                { required: true, message: 'Please enter subgroup size' },
                { type: 'number', min: 2, max: 25, message: 'Subgroup size must be between 2 and 25' }
              ]}
            >
              <InputNumber
                min={2}
                max={25}
                placeholder="Enter subgroup size (e.g., 5)"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <div className="flex justify-end gap-2 mt-4">
              <Button onClick={handleClose}>
                Cancel
              </Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={loading}
              >
                Save Configuration
              </Button>
            </div>
          </div>
        );

      case 'ScatterPlotPanel':
        return (
          <div>
            <h3 className="text-lg font-medium mb-4">Scatter Plot Configuration</h3>
            {targetVariables.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-2">No target variables found in system configuration</p>
                <p className="text-sm text-gray-400">Please configure target variables in your system settings to enable scatter plot functionality</p>
              </div>
            ) : (
              <>
                <Form.Item
                  name="targetVariable"
                  label="Target Variable (X-axis)"
                  rules={[
                    { required: true, message: 'Please select a target variable' }
                  ]}
                >
                  <Select
                    placeholder="Select target variable"
                    style={{ width: '100%' }}
                    options={targetVariableOptions}
                  />
                </Form.Item>

                <div className="flex justify-end gap-2 mt-4">
                  <Button onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleSubmit}
                    loading={loading}
                  >
                    Save Configuration
                  </Button>
                </div>
              </>
            )}
          </div>
        );

      default:
        return (
          <div>
            <p>No configuration options available for this panel type.</p>
          </div>
        );
    }
  };

  return (
    <Drawer
      title={
        <div className="flex items-center gap-2">
          <SettingOutlined />
          <span>Panel Configuration</span>
        </div>
      }
      open={isOpen}
      onClose={handleClose}
      width={400}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          subgroupSize: 5, // Default subgroup size
          targetVariable: defaultTargetVariable || undefined // Default target variable from systems, undefined if none available
        }}
      >
        {renderConfigurationContent()}
      </Form>
    </Drawer>
  );
};

export default PanelConfigDrawer;
