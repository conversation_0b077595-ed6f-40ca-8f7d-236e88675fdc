import React from 'react';
import ReactDOM from 'react-dom';
import { CloseOutlined } from '@ant-design/icons';
import './FullScreenModal.css';

interface FullScreenModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const FullScreenModal: React.FC<FullScreenModalProps> = ({
  isOpen,
  onClose,
  title,
  children
}) => {
  if (!isOpen) return null;

  // Create portal to render modal directly in document body
  return ReactDOM.createPortal(
    <div className="full-screen-overlay">
      <div className="full-screen-content">
        <div className="full-screen-header">
          <h2>{title}</h2>
          <button
            className="close-button"
            onClick={onClose}
            aria-label="Close full screen"
          >
            <CloseOutlined />
          </button>
        </div>
        <div className="full-screen-body">
          {children}
        </div>
      </div>
    </div>,
    document.body // Render directly in the document body
  );
};

export default FullScreenModal;
