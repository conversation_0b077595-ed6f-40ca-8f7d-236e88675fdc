/* Grid Container */
.grid-container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: auto;
  position: relative;
}

/* Drop Indicator */
.drop-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2px dashed #ccc;
  border-radius: 8px;
  text-align: center;
  color: #666;
  font-size: 16px;
  z-index: 10;
}

/* Grid Item */
.grid-item {
  width: 100%;
  height: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Grid Item Header */
.grid-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.grid-item-title {
  font-weight: 500;
  font-size: 14px;
  cursor: move;
  flex-grow: 1;
}

.grid-item-controls {
  display: flex;
  gap: 4px;
}

.grid-item-control {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  font-size: 12px;
  color: #666;
  border-radius: 2px;
}

.grid-item-control:hover {
  background-color: #e0e0e0;
}

/* Expand button */
.grid-item-control .anticon-expand-outlined {
  color: #1890ff;
  font-size: 14px;
}

/* Expand button hover effect */
.grid-item-control:hover .anticon-expand-outlined {
  color: #40a9ff;
  transform: scale(1.1);
}

/* Panel Options Menu */
.panel-options-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  font-size: 16px;
  color: #666;
  border-radius: 2px;
  height: 24px;
  width: 24px;
}

.panel-options-button:hover {
  background-color: #e0e0e0;
}

/* Grid Item Content */
.grid-item-content {
  flex-grow: 1;
  overflow: auto;
  padding: 8px;
}

/* Fullscreen Mode */
.grid-item.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
}

/* Drag Handle */
.drag-handle {
  cursor: move;
}

/* No Drag Area */
.no-drag {
  cursor: default;
}

/* Component List */
.component-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.component-item {
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.component-item:hover {
  background-color: #f5f5f5;
}

.component-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.added-badge {
  font-size: 10px;
  padding: 2px 4px;
  background-color: #e0e0e0;
  border-radius: 4px;
  color: #666;
}

/* Selected Row/Column Highlighting */
.selected-row {
  background-color: rgba(100, 108, 255, 0.1) !important;
}

.selected-column {
  background-color: rgba(100, 108, 255, 0.1) !important;
}

/* Interactive Content */
.interactive-content {
  width: 100%;
  height: 100%;
}

/* Statistics Panel */
.statistics-panel {
  padding: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 18px;
  font-weight: 500;
}

/* Date Filter Panel */
.date-filter-panel {
  margin-bottom: 12px;
}

/* View Sidebar */
.view-sidebar {
  height: 100%;
  border-right: 1px solid #e0e0e0;
}

/* View Content */
.view-content {
  height: calc(100vh - 85px);
}

/* Applied Filters */
.applied-filters {
  padding: 8px 0;
}

.applied-filters .ant-tag {
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  font-size: 12px;
}

.applied-filters .ant-tag .anticon-close {
  margin-left: 4px;
  font-size: 10px;
}

.applied-filters .ant-tag-blue {
  background-color: rgba(24, 144, 255, 0.1);
  border-color: #1890ff;
  color: #1890ff;
}

.applied-filters .ant-tag-green {
  background-color: rgba(82, 196, 26, 0.1);
  border-color: #52c41a;
  color: #52c41a;
}

.applied-filters .ant-tag-purple {
  background-color: rgba(114, 46, 209, 0.1);
  border-color: #722ed1;
  color: #722ed1;
}


