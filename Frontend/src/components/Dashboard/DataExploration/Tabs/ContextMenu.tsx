import { useEffect, useRef } from "react";
import { ContextMenuAction } from "./Types/tabTypes";

interface ContextMenuProps {
  x: number;
  y: number;
  tabId: string;
  onAction: (action: ContextMenuAction, tabId: string) => void;
  onClose: () => void;
}

export default function ContextMenu({
  x,
  y,
  tabId,
  onAction,
  onClose
}: ContextMenuProps) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {

      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    // Add click event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div 
      ref={menuRef}
      className="context-menu"
      style={{ left: `${x}px`, top: `${y}px` }}
    >
      <ul className="py-1">
        <li 
          className="context-menu-item"
          onClick={() => onAction("closeTab", tabId)}
        >
          Close
        </li>
        <li 
          className="context-menu-item"
          onClick={() => onAction("closeAllTabs", tabId)}
        >
          Close All
        </li>
        <li 
          className="context-menu-item"
          onClick={() => onAction("closeOtherTabs", tabId)}
        >
          Close Others
        </li>
        <li 
          className="context-menu-item"
          onClick={() => onAction("closeTabsToRight", tabId)}
        >
          Close Tabs to the Right
        </li>
        <li className="border-t border-[#454545] my-1"></li>
      </ul>
    </div>
  );
}
