import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Collapse, Typography } from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  MinusOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { PLCComponentType } from './types/PLCTypes';

const { Panel } = Collapse;
const { Title, Text } = Typography;

interface PLCSidebarProps {
  onBackClick?: () => void;
  activePanels?: PLCComponentType[];
  availableDataSources?: any[];
  onDataSourceSelect?: (dataSource: any) => void;
}

const PLCSidebar: React.FC<PLCSidebarProps> = ({
  onBackClick,
  activePanels = [],
  availableDataSources = [], // Will be used in future tasks
  onDataSourceSelect // Will be used in future tasks
}) => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
      navigate('/?tab=data');
    } else {
      // Default behavior: navigate back to data tab
      navigate('/?tab=data');
    }
  };

  // Check if panels are already added
  const isBatchComparisonAdded = activePanels.includes(PLCComponentType.BatchComparisonPanel);
  const isPhaseIdentificationAdded = activePanels.includes(PLCComponentType.PhaseIdentificationPanel);

  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4 h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back
          </Button>

          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Title level={4} className="text-gray-700 mb-2">PLC Level Exploration</Title>
            <Text className="text-gray-500 text-sm">Drag and drop panels to analyze PLC data</Text>
          </div>
        </Space>
      </div>

      {/* Panels Section */}
      <div className="flex-1">
        <Collapse
          defaultActiveKey={['panels']}
          expandIconPosition="end"
          expandIcon={({ isActive }) => (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {isActive ?
                <MinusOutlined style={{ fontSize: '16px' }} /> :
                <PlusOutlined style={{ fontSize: '16px' }} />
              }
            </div>
          )}
          className="plc-sidebar-collapse"
        >
          <Panel
            header={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Title level={5} style={{ margin: 0 }}>Panels</Title>
              </div>
            }
            key="panels"
            style={{
              borderRadius: 0,
              backgroundColor: '#E9E9F5',
              marginBottom: '15px',
              border: '1px solid #b7b6b6'
            }}
          >
            <div className="plc-panels-list">
              <ul className="component-list">
                {/* Batch Comparison Panel */}
                <li
                  className={`component-item p-2 mb-2 rounded cursor-pointer ${
                    isBatchComparisonAdded
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-100'
                  }`}
                  draggable={!isBatchComparisonAdded}
                  onDragStart={(e) => {
                    if (isBatchComparisonAdded) {
                      e.preventDefault();
                      return;
                    }
                    e.dataTransfer.setData('component', PLCComponentType.BatchComparisonPanel);
                    e.dataTransfer.effectAllowed = 'move';
                  }}
                  onDragEnd={() => {
                    // Reset any drag styling if needed
                  }}
                >
                  Batch Comparison {isBatchComparisonAdded && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
                </li>

                {/* Phase Identification Panel */}
                <li
                  className={`component-item p-2 mb-2 rounded cursor-pointer ${
                    isPhaseIdentificationAdded
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-100'
                  }`}
                  draggable={!isPhaseIdentificationAdded}
                  onDragStart={(e) => {
                    if (isPhaseIdentificationAdded) {
                      e.preventDefault();
                      return;
                    }
                    e.dataTransfer.setData('component', PLCComponentType.PhaseIdentificationPanel);
                    e.dataTransfer.effectAllowed = 'move';
                  }}
                  onDragEnd={() => {
                    // Reset any drag styling if needed
                  }}
                >
                  Phase Identification {isPhaseIdentificationAdded && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
                </li>
              </ul>
            </div>
          </Panel>
        </Collapse>
      </div>
    </div>
  );
};

export default PLCSidebar;
