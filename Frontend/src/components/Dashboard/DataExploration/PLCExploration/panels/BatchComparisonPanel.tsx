import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Button, Spin, Empty, message } from 'antd';
import { SettingOutlined, LineChartOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import {
  BatchComparisonPanelProps
} from '../types/PLCTypes';
import { postRequest } from '../../../../../utils/apiHandler';


const BatchComparisonPanel: React.FC<BatchComparisonPanelProps> = ({
  configuration,
  isFullScreen = false,
  hasError = false,
  errorMessage,
  onOpenConfiguration,
  onCreatePhaseIdentification
}) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [isChartReady, setIsChartReady] = useState(false);
  const [chartUpdateTrigger, setChartUpdateTrigger] = useState(0);
  const [contextMenu, setContextMenu] = useState<{ visible: boolean; x: number; y: number; batchId: string } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const echartsRef = useRef<any>(null);
  const chartWrapperRef = useRef<HTMLDivElement>(null);
  const contextMenuRef = useRef<HTMLDivElement>(null);


  const isConfigured = configuration &&
                      configuration.dataRange?.startDate &&
                      configuration.dataRange?.endDate;

  // Check if Y-axis columns are selected
  const hasSelectedColumns = configuration?.basic?.selectedColumns?.headers &&
                             configuration.basic.selectedColumns.headers.length > 0;

  // Use API data if available - updated for new API structure
  const hasApiData = configuration?.apiData?.columns && configuration.apiData.columns.length > 0;
  const availableColumns = hasApiData && configuration?.apiData?.columns
    ? configuration.apiData.columns.filter((col: string) => col !== 'DateTime')
    : [];

  // Only show data if Y-axis columns are explicitly selected
  const columnsToDisplay = hasSelectedColumns ? configuration.basic?.selectedColumns?.headers || [] : [];

  // Create selectedColumns state that matches the batch exploration pattern
  const effectiveSelectedColumns = {
    indices: columnsToDisplay.map(col => availableColumns.indexOf(col)).filter(idx => idx !== -1),
    headers: columnsToDisplay
  };

  // Context menu handlers
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        setContextMenu(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get the actual color used by ECharts for a series
  const getSeriesColor = (series: any, index: number) => {
    // Use the same logic as ECharts series configuration
    return series.color || series.lineStyle?.color || '#1f77b4';
  };

  // Handle phase comparison for selected batch
  const handlePhaseIdentification = async (batchId: string) => {
    if (onCreatePhaseIdentification) {
      onCreatePhaseIdentification(batchId);
    } else {
      message.info('Phase comparison functionality not available');
    }
    setContextMenu(null);
  };

  // Monitor container size changes
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { offsetWidth, offsetHeight } = containerRef.current;
        setContainerSize({ width: offsetWidth, height: offsetHeight });
        setIsChartReady(offsetWidth > 0 && offsetHeight > 0);
      }
    };

    updateSize();
    
    const resizeObserver = new ResizeObserver(updateSize);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  // Handle configuration button click
  const handleConfigureClick = () => {
    if (onOpenConfiguration) {
      // Pass currently displayed columns to the configuration
      onOpenConfiguration(columnsToDisplay);
    }
  };

  // Always show stacked view - no tabs needed
  const displayedColumnsCount = effectiveSelectedColumns.headers.length;

  // Force chart re-render when configuration changes
  useEffect(() => {

    // Clear the chart instance completely to prevent grid/axis misalignment
    if (echartsRef.current) {
      const chartInstance = echartsRef.current.getEchartsInstance();
      if (chartInstance) {
        chartInstance.dispose();
      }
    }

    // Trigger re-render with optimized delay
    const timeoutId = setTimeout(() => {
      setChartUpdateTrigger(prev => prev + 1);
    }, 50); // Reduced delay for better performance

    return () => clearTimeout(timeoutId);
  }, [columnsToDisplay.join(','), configuration?.basic?.group, configuration?.basic?.selectedColumns?.headers?.join(','), configuration?.basic?.xAxisColumn]);

  // Create plot data using API data only
  const plotData = useMemo(() => {

    // Use columnOptions format from the new API structure
    if (hasApiData && configuration?.apiData?.columnOptions) {
      const columnOptions = configuration.apiData.columnOptions;
      
      // Create quality lookup map for quick access
      const qualityLookup = (configuration?.apiData?.qualityBatchIds || []).reduce((acc: any, batch: any) => {
        acc[batch.batch_id] = batch;
        return acc;
      }, {});

      // Convert API data to our internal format
      // API returns data under columnOptions[columnName].series as array of series (batches)
      const result: any[] = [];
      
      effectiveSelectedColumns.headers.forEach(columnName => {
        const columnData = columnOptions[columnName];

        if (columnData?.series && Array.isArray(columnData.series)) {
          // Process all series for this column (all batches)
          columnData.series.forEach((series: any, seriesIndex: number) => {
            if (series && series.data && series.data.length > 0) {
              // Extract batch ID from series name (assuming format like "BatchId_XXX" or just "XXX")
              const batchId = series.name || `Batch_${seriesIndex + 1}`;
              
              // Calculate start and end times for this batch
              const timestamps = series.data.map((point: any) => new Date(point[0]).getTime());
              const startTime = Math.min(...timestamps);
              const endTime = Math.max(...timestamps);
              
              // Get quality information for this batch
              const qualityInfo = qualityLookup[batchId] || {
                batch_quality: 0,
                is_batch_good: false
              };

              // Convert datetime-based data to index-based data for overlapping
              const indexBasedData = series.data.map((point: any, index: number) => {
                // Convert [datetime, value] to [index, value] for superposition
                return [index, point[1]]; // point[0] is datetime, point[1] is value
              });

              result.push({
                name: series.name || `${columnName} - Series ${seriesIndex + 1}`,
                // data: indexBasedData, // Now using index-based data for overlapping
                data: series.data, // Use datetime-based data for batch comparison
                groupValue: series.name || null,
                originalColumn: columnName,
                lineStyle: series.lineStyle || { width: 2 },
                symbolSize: series.symbolSize || 4,
                color: series.color || series.lineStyle?.color || null,
                seriesIndex: seriesIndex,
                columnName: columnName,
                // Enhanced batch metadata
                batchId: batchId,
                batchStartTime: startTime,
                batchEndTime: endTime,
                batchQuality: qualityInfo.batch_quality,
                isBatchGood: qualityInfo.is_batch_good
              });
            }
          });
        }
      });

      return result;
    }

    // No fallback - return empty array if no API data
    return [];
  }, [hasApiData, configuration?.apiData?.columnOptions, configuration?.apiData?.qualityBatchIds, effectiveSelectedColumns.headers]);

  // Create true stacked ECharts option like batch exploration
  const createStackedOption = useMemo(() => {
    if (!plotData || plotData.length === 0) {
      return {
        series: [],
        xAxis: { type: 'time' },
        yAxis: { type: 'value' },
        hasData: false
      };
    }

    // Get X-axis column and determine its type
    const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';
    const xAxisType = xAxisColumn === 'DateTime' ? 'time' : 'value'; // Use 'time' for DateTime, 'value' for others

    // Group series by original column to create separate charts
    const seriesByColumn = plotData.reduce((acc: any, series: any) => {
      const columnName = series.columnName;
      if (!acc[columnName]) {
        acc[columnName] = [];
      }
      acc[columnName].push(series);
      return acc;
    }, {});

    const columnNames = Object.keys(seriesByColumn);

    // Calculate fixed Y-axis ranges for each column to lock Y-axis
    const yAxisRanges: {[key: string]: {min: number, max: number}} = {};
    columnNames.forEach(columnName => {
      let min = Infinity;
      let max = -Infinity;
      
      seriesByColumn[columnName].forEach((series: any) => {
        if (series.data && Array.isArray(series.data)) {
          series.data.forEach((point: any) => {
            if (point && Array.isArray(point) && typeof point[1] === 'number') {
              min = Math.min(min, point[1]);
              max = Math.max(max, point[1]);
            }
          });
        }
      });

      if (min !== Infinity && max !== -Infinity) {
        // Add 5% padding
        const padding = (max - min) * 0.05;
        yAxisRanges[columnName] = {
          min: min - padding,
          max: max + padding
        };
      }
    });

    // Create simple stacked chart like batch exploration
    return {
      animation: false,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params: any) {
          if (!params || params.length === 0) return '';
          
          // Group tooltip content by batch to avoid repetition
          const batchGroups: {[key: string]: any} = {};
          
          params.forEach((param: any) => {
            // Find the corresponding plotData entry for this series
            const seriesData = plotData.find(p => p.name === param.seriesName);
            
            if (seriesData) {
              const batchId = seriesData.batchId;
              
              if (!batchGroups[batchId]) {
                batchGroups[batchId] = {
                  batchId: batchId,
                  startTime: new Date(seriesData.batchStartTime).toLocaleString(),
                  endTime: new Date(seriesData.batchEndTime).toLocaleString(),
                  quality: seriesData.batchQuality,
                  isGood: seriesData.isBatchGood,
                  series: []
                };
              }
              
              batchGroups[batchId].series.push({
                name: param.seriesName,
                value: param.value,
                color: param.color,
                columnName: seriesData.columnName
              });
            }
          });
          
                     let tooltipHtml = '';
           Object.values(batchGroups).forEach((batch: any) => {
             const qualityColor = batch.isGood ? '#52c41a' : '#ff4d4f';
             const qualityText = batch.isGood ? 'Good' : 'Bad';
             
             tooltipHtml += `
               <div style="
                 background: linear-gradient(135deg, ${qualityColor}15, ${qualityColor}05);
                 border: 1px solid ${qualityColor}40;
                 border-radius: 8px;
                 padding: 12px;
                 margin-bottom: 8px;
                 box-shadow: 0 2px 8px rgba(0,0,0,0.1);
               ">
                 <div style="
                   font-weight: 600;
                   font-size: 14px;
                   color: ${qualityColor};
                   margin-bottom: 8px;
                   display: flex;
                   align-items: center;
                   gap: 6px;
                 ">
                   <span style="color: #333;">Batch:</span> ${batch.batchId}
                 </div>
                
                <div style="
                  display: grid;
                  grid-template-columns: auto 1fr;
                  gap: 4px 8px;
                  font-size: 12px;
                  color: #666;
                  margin-bottom: 8px;
                ">
                  <span style="font-weight: 500;">⏰ Start:</span>
                  <span>${batch.startTime}</span>
                  <span style="font-weight: 500;">⏰ End:</span>
                  <span>${batch.endTime}</span>
                  <span style="font-weight: 500;">🎯 Quality:</span>
                  <span style="color: ${qualityColor}; font-weight: 600;">
                    ${qualityText} (${batch.quality.toFixed(2)})
                  </span>
                </div>
                
                <div style="
                  border-top: 1px solid #f0f0f0;
                  padding-top: 8px;
                  font-size: 12px;
                ">
                  ${batch.series.map((s: any) => `
                    <div style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      padding: 2px 0;
                    ">
                      <span style="
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        color: #333;
                        font-weight: 500;
                      ">
                        <span style="
                          display: inline-block;
                          width: 8px;
                          height: 8px;
                          border-radius: 50%;
                          background: ${s.color};
                        "></span>
                        ${s.columnName}
                      </span>
                      <span style="
                        font-weight: 600;
                        color: #1890ff;
                      ">
                        ${Array.isArray(s.value) ? s.value[1]?.toFixed(2) : s.value?.toFixed(2)}
                      </span>
                    </div>
                  `).join('')}
                </div>
              </div>
            `;
          });
          
          return tooltipHtml;
        }
      },
      brush: {
        xAxisIndex: columnNames.map((_, i) => i), // Only allow brush on X-axis
        yAxisIndex: [] // Disable brush on Y-axis
      },
      legend: {
        // show: false
        show: true,
        type: 'scroll',
        orient: 'horizontal',
        top: 'top',
        left: 'center',
        textStyle: {
          fontSize: 12,
          color: '#333'
        },
        itemGap: 10,
        itemWidth: 14,
        itemHeight: 14
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: columnNames.map((_, i) => i),
          yAxisIndex: [],
          filterMode: 'filter'
        },
        {
          type: 'slider',
          xAxisIndex: columnNames.map((_, i) => i),
          yAxisIndex: [],
          filterMode: 'filter'
        }
      ],
      grid: columnNames.map((_, index) => {
        // Optimize grid spacing for full screen mode like batch exploration
        const leftMargin = isFullScreen ? '1%' : '10px';
        const rightMargin = isFullScreen ? '2%' : '10px';
        const topBase = isFullScreen ? 2 : 15;
        const heightBase = isFullScreen ? 96 : 75;
        const bottomMargin = isFullScreen ? 2 : 10;

        return {
          left: leftMargin,
          right: rightMargin,
          top: `${topBase + index * (heightBase / columnNames.length)}%`,
          height: `${(heightBase - bottomMargin) / columnNames.length}%`,
          containLabel: true
        };
      }),
      xAxis: columnNames.map((columnName, index) => ({
        type: xAxisType,
        gridIndex: index,
        name: xAxisColumn,
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#f0f0f0',
            width: 1,
            type: 'solid'
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#666'
          }
        },
        axisTick: {
          show: true
        },
        axisLabel: {
          show: true,
          fontSize: 11,
          color: '#666'
        }
      })),
      yAxis: columnNames.map((columnName, index) => ({
        type: 'value',
        gridIndex: index,
        name: columnName,
        nameLocation: 'middle',
        nameGap: 50,
        nameTextStyle: {
          fontSize: 12,
          fontWeight: 'bold'
        },
        scale: false,
        silent: true,
        min: yAxisRanges[columnName]?.min,
        max: yAxisRanges[columnName]?.max,
        axisLabel: {
          formatter: function (value: any) {
            return typeof value === 'number' ? value.toFixed(2) : value;
          }
        }
      })),
      series: plotData.map((s, index) => {
        // Find which column index this series belongs to
        const columnIndex = columnNames.indexOf(s.columnName);
        
        return {
          name: s.name,
          type: 'line',
          xAxisIndex: columnIndex,
          yAxisIndex: columnIndex,
          data: s.data || [],
          smooth: false,
          symbol: 'circle',
          symbolSize: s.symbolSize || 4,
          lineStyle: {
            width: s.lineStyle?.width || 2,
            color: s.color || s.lineStyle?.color || '#1f77b4'
          },
          itemStyle: {
            color: s.color || s.lineStyle?.color || '#1f77b4'
          },
          emphasis: {
            focus: 'series'
          }
        };
      }),
      hasData: true
    };
  }, [plotData, isFullScreen]);

  // Removed unused tab functions since we're using direct chart rendering

  const handleDownloadPDF = async () => {
    if (!chartWrapperRef.current) return;
    const canvas = await html2canvas(chartWrapperRef.current, { useCORS: true });
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF({
      orientation: "landscape",
      unit: "px",
      format: [canvas.width, canvas.height],
    });
    pdf.addImage(imgData, "PNG", 0, 0, canvas.width, canvas.height);
    pdf.save("batch-comparison-chart.pdf");
  };

  // Render empty state with configuration prompt when panel is not configured
  if (!configuration || !isConfigured) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<LineChartOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-700 mb-2">
                {!configuration ? 'Panel Not Configured' : 'Configuration Incomplete'}
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {!configuration
                  ? 'Configure this panel to display PLC batch comparison data'
                  : 'Please complete the configuration by selecting date range and columns, then click Submit'
                }
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
              >
                Configure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show error state if API data has error
  if (hasApiData && configuration?.apiData?.metadata?.error) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<LineChartOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Data Loading Error
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {configuration.apiData.metadata.error}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
              >
                Reconfigure Panel
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  // Show empty state if no Y-axis columns are selected
  if (isConfigured && hasApiData && columnsToDisplay.length === 0) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<LineChartOutlined style={{ fontSize: '48px', color: '#1890ff' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-gray-800 mb-2">
                No Y-axis Columns Selected
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                Please select one or more columns for the Y-axis to display the chart.
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
              >
                Select Columns
              </Button>
            </div>
          }
        />
      </div>
    );
  }


  // Show error state if there's an error
  if (hasError) {
    return (
      <div
        ref={containerRef}
        className="flex items-center justify-center h-full"
        style={{ minHeight: '300px' }}
      >
        <Empty
          image={<LineChartOutlined style={{ fontSize: '48px', color: '#ff4d4f' }} />}
          description={
            <div className="text-center">
              <h3 className="text-base font-medium text-red-600 mb-2">
                Error Loading Batch Comparison Data
              </h3>
              <p className="text-gray-500 mb-4 text-sm">
                {errorMessage || 'Failed to load batch comparison data. Please try again.'}
              </p>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigureClick}
              >
                Retry Configuration
              </Button>
            </div>
          }
        />
      </div>
    );
  }

  const xAxisColumn = configuration?.basic?.xAxisColumn || 'DateTime';

  return (
    <div className={`time-series-panel ${isFullScreen ? 'h-full' : 'h-[95%]'}`} ref={containerRef} style={{ position: 'relative' }}>
      <div className={`flex justify-between items-center ${isFullScreen ? 'mb-0 px-2 pt-0' : 'mb-1 px-3 pt-1'}`}>
        <h3 className="text-base font-medium">
          Batch Comparison ({displayedColumnsCount} columns, {plotData.length} series) - X-axis: {xAxisColumn}
        </h3>
        <Button onClick={handleDownloadPDF} icon={<LineChartOutlined />}>
          Download PDF
        </Button>
      </div>
      <div className={`${isFullScreen ? 'p-0 h-[calc(100%-100px)]' : 'p-2 h-[calc(100%-30px)]'} overflow-auto`}>
        {/*
        ============================================================================
        CUSTOM LEGEND TEMPORARILY DISABLED - USING ECHARTS DEFAULT LEGEND INSTEAD
        ============================================================================
        Custom Legend with Right-Click Support
        {plotData.length > 0 && (
          <div style={{
            padding: '8px 16px',
            borderBottom: '1px solid #f0f0f0',
            background: '#fafafa',
            marginBottom: '8px'
          }}>
            <div style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '6px',
              alignItems: 'center'
            }}>
              {plotData.map((series, index) => {
                const seriesColor = getSeriesColor(series, index);

                return (
                  <div
                    key={series.name}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '4px',
                      padding: '3px 6px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      background: '#fff',
                      border: '1px solid #e8e8e8',
                      fontSize: '11px',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={(e) => {
                      e.preventDefault();

                      // Show menu centered in panel using CSS transform
                      setContextMenu({
                        visible: true,
                        x: 0, // Not used with CSS transform centering
                        y: 0, // Not used with CSS transform centering
                        batchId: series.name
                      });
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#f0f8ff';
                      e.currentTarget.style.borderColor = '#1890ff';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = '#fff';
                      e.currentTarget.style.borderColor = '#e8e8e8';
                    }}
                    title="Click to load phase comparison"
                  >
                    <div
                      style={{
                        width: '10px',
                        height: '10px',
                        borderRadius: '50%',
                        background: seriesColor
                      }}
                    />
                    <span>{series.name}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        ============================================================================
        END OF COMMENTED CUSTOM LEGEND CODE
        ============================================================================
        */}

        <div
          ref={chartWrapperRef}
          // style={{
          //   height: plotData.length > 0 ? 'calc(100% - 60px)' : '100%',
          //   minHeight: isFullScreen ? 'calc(100% - 60px)' : `${Math.max(340, displayedColumnsCount * 150)}px`
          // }}
          style={{
            height: '100%',
            minHeight: isFullScreen ? '100%' : `${Math.max(340, displayedColumnsCount * 150)}px`
          }}
        >
          {containerSize.width > 0 && containerSize.height > 0 && isChartReady ? (
            <ReactECharts
              key={`stacked-${chartUpdateTrigger}`}
              ref={echartsRef}
              option={createStackedOption}
              style={{
                width: '100%',
                height: '100%',
                ...(isFullScreen ? {} : { minHeight: '400px' })
              }}
              opts={{ renderer: 'canvas' }}
              notMerge={true}
              onEvents={{
                'legendselectchanged': (params: any) => {
                  // Handle legend selection if needed
                }
              }}
            />
          ) : (
            <div style={{
              width: '100%',
              height: isFullScreen ? '100%' : '400px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Spin size="large" tip="Loading chart..." />
            </div>
          )}
        </div>
      </div>

      {/*
      ============================================================================
      CONTEXT MENU TEMPORARILY DISABLED (WAS TIED TO CUSTOM LEGEND)
      ============================================================================
      Context Menu for Phase Comparison
      {contextMenu?.visible && (
        <>
          Backdrop overlay
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-20"
            onClick={() => setContextMenu(null)}
          />

          Context Menu
          <div
            ref={contextMenuRef}
            className="absolute z-50 bg-white shadow-xl border rounded-lg p-1 text-sm"
            style={{
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)', // Perfect centering like TimeSeriesPanel
              minWidth: '220px',
              boxShadow: '0 8px 24px rgba(0,0,0,0.25)',
              transition: 'all 0.15s ease-out'
            }}
          >
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="font-semibold text-gray-800 flex items-center gap-2">
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    background: '#1890ff'
                  }}
                />
                Batch: {contextMenu.batchId}
              </div>
            </div>
            <button
              className="block w-full text-left px-4 py-3 hover:bg-blue-50 text-blue-600 font-medium transition-colors duration-150 rounded-b-lg"
              onClick={() => handlePhaseIdentification(contextMenu.batchId)}
            >
              🔬 Load Phase Comparison
            </button>
          </div>
        </>
      )}
      ============================================================================
      END OF COMMENTED CONTEXT MENU CODE
      ============================================================================
      */}
    </div>
  );
};

export default BatchComparisonPanel; 