import { Flex, message } from "antd";
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
} from "react";
import data from "../../img/data.svg";
import insight from "../../img/insight.svg";
import arrowleft from "../../img/arrow-left.svg";

import ParallelTimeSeriesChart from "../charts/ParallelTimeSeriesChart";
import DataTable from "../tables/DataTable";
import ReactFlow, { Node } from "reactflow";
import "reactflow/dist/style.css";
import FlowCanvas from "./FlowCanvas";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import QueryBuilder from "./QueryBuilder";
import OperationConfig from "./OperationConfig";
import file from "../../img/file.svg";
import box from "../../img/box.svg";
import { getRequest } from "../../utils/apiHandler";
import Notiflix from "notiflix";
import InsightTabContent from "./Insight Section/InsightTabContent";
import { useLocation, useNavigate } from "react-router-dom"; // Import useLocation from React Router
import { Histogram } from "../charts/Histogram";
import { Menu } from "antd";
import { useAuth } from "../../context/AuthContext";
import DataTab from "./Data Section/DataTab";
import InsightContent from "./Insight Section/InsightTab";
import InsightTab from "./Insight Section/InsightTab";
import AlertTab from "./Alert Section/AlertTab";
import AlertTabContent from "./Alert Section/AlertTabContent";
import DataTabContent from "./Data Section/DataTabContent";
import FolderStructure from "./FolderStructure";
import "./DataExploration/ExplorationStyles.css";
import ExplorationSidebar from "./DataExploration/BatchExploration/ExplorationSidebar";
import {
  ComponentType,
  ColumnSelection,
  DateFilter,
} from "./DataExploration/types";
import {
  PanelFilter,
  createColumnFilter,
  createDateFilter,
  createValueRangeFilter,
  ValueRangeFilter,
  updateFilter,
} from "./DataExploration/FilterTypes";
import { useFetchFilteredFileData } from "../../hooks/useFetchFilteredFileData";
import { useSelector } from "react-redux";
import ExplorationContent from "./DataExploration/BatchExploration/ExplorationContent";
import PLCContent from "./DataExploration/PLCExploration/PLCContent";
import PLCSidebar from "./DataExploration/PLCExploration/PLCSidebar";
import { useDispatch } from "react-redux";
import { addTab } from "../../Redux/slices/viewTabSlice";
import { toggleAnnotationFilter } from "../../Redux/slices/annotationSlice";
import {
  toggleOperationFilter,
  toggleSelectedOperation,
} from "../../Redux/slices/operationSlice";
import {
  hideFullscreenLoader,
  showFullscreenLoader,
} from "../Common/CommonLoader";
import DateRangeFilter from "../DataRangeFilter";
interface FileData {
  csv_id: string;
  file_name: string;
  file_path: string;
  version: number;
  created_at?: string;
  updated_at?: string;
  data?: any;
}

function WorkbookContainer() {
  const [activeTab, setActiveTab] = useState("tab1");
  const [collapse, setCollapse] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileData[]>([]);
  const [originalChartData, setOriginalChartData] = useState<any>({
    timeSeries: { categories: [], series: [] },
  });
  const [filteredChartData, setFilteredChartData] =
    useState<any>(originalChartData);
  const [originalFileData, setOriginalFileData] = useState<any>([]);
  const [filteredFileData, setFilteredFileData] = useState<any[]>([]);
  const [showVisualisation, setShowVisualization] = useState<Boolean>(true);
  const [dataContentSection, setDataContentSection] = useState<
    | "file-listing"
    | "data-visualisation"
    | "golden-parameters"
    | "golden-parameters-details"
    | "create-parameters"
  >("file-listing");
  const [nodes, setNodes] = useState<Node[]>([]);
  const [fileQueries, setFileQueries] = useState<Record<string, any>>({});
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [showQueryBuilder, setShowQueryBuilder] = useState(false);
  const [showOperationConfig, setShowOperationConfig] = useState(false);
  const [queries, setQueries] = useState({});
  const [nodeConfigurations, setNodeConfigurations] = useState<
    Record<string, any>
  >({});
  const [panelConfigurations, setPanelConfigurations] = useState<
    Record<string, any>
  >({});
  const [workbookId, setWorkbookId] = useState(1);
  const [activeDashboard, setActiveDashboard] = useState<string>("");

  // Dynamic SubPlot Data & Layout State
  const [dynamicPlotData, setDynamicPlotData] = useState<any>([]);
  const [dynamicPlotLayout, setDynamicPlotLayout] = useState<any>([]);
  const [flowType, setFlowType] = useState<any>("workflow");
  const [explorationType, setExplorationType] = useState<'batch' | 'plc'>('batch');

  // const [selectSystems,setSelectSystems] = useState<any>([]);
  // const [reloadSelectSystems,setReloadSelectSystems] = useState<boolean>(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const dispatch = useDispatch();
  //States for views Section
  const [files, setFiles] = useState<FileData[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null);
  const [fileSelected, setFileSelected] = useState(false);
  const [initialPanelsCreated, setInitialPanelsCreated] = useState(false);
  const [activePanels, setActivePanels] = useState<ComponentType[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(true);

  // State for selections and filters
  const [selectedColumns, setSelectedColumns] = useState<ColumnSelection>({
    indices: [],
    headers: [],
  });
  const [dateFilter, setDateFilter] = useState<DateFilter>({
    startDate: null,
    endDate: null,
  });

  // State for conditional column filters (from Handsontable)
  const [conditionalFilters, setConditionalFilters] = useState<PanelFilter[]>(
    []
  );

  // New filter state system
  const [panelFilters, setPanelFilters] = useState<
    Record<string, PanelFilter[]>
  >({});

  // Initialize panel filters for data table and overview panels
  useEffect(() => {
    setPanelFilters({
      "data-table-panel": [],
      "overview-panel": [],
      "histogram-panel": [],
      "time-series-panel": [],
    });
  }, []);
  const auth = useAuth();
  const selectSystems = useSelector((state: any) => state.systems.systems);

  // Get annotations and operations from Redux store at the component level
  const annotationsState = useSelector((state: any) => state.annotations);
  const operationsState = useSelector((state: any) => state.operations);

  const [viewTabOpen, setViewTabOpen] = useState(false);
  console.log("viewTabOpen :", viewTabOpen);
  //Functions for views section

  // Function to reset all view-related states
  const resetViewStates = () => {
    console.log("Resetting view states");
    // setSelectedFile(null);
    // setFileSelected(false);
    // setInitialPanelsCreated(false);
    // setActivePanels([]);
    // setSelectedRows({ indices: [], data: [] });
    // setSelectedColumns({ indices: [], headers: [] });
    // setDateFilter({ startDate: null, endDate: null });
    // setViewStructure(null);
    // setFlowType('view');
    // setViewTabOpen(true)
    // showFolderComponent = true

    // Navigate back to workflow folders
    // navigate('/?tab=insight&viewId=0');
  };

  // Handle file selection - Updated to use unified API
  const handleFileSelect = async (file: FileData) => {
    try {
      // Set flag to prevent logging during file selection reset
      setIsFileSelectionInProgress(true);

      setSelectedFile(file);
      setFileSelected(true);

      // Only reset panels if no panels are currently active (first time file selection)
      if (activePanels.length === 0) {
        setInitialPanelsCreated(false); // This will trigger creation of initial 4 panels
      } else {
        // Keep existing panels but clear their data for the new file
        setInitialPanelsCreated(true); // Prevent creating new panels
      }

      // Reset selections and filters when file changes
      setSelectedColumns({ indices: [], headers: [] });
      setDateFilter({ startDate: null, endDate: null });
      setConditionalFilters([]);

      // Clear any existing panel data for the new file (only for current exploration type)
      setPanelSpecificData({
        [ComponentType.TimeSeriesPanel]: null,
        [ComponentType.OverviewPanel]: null,
        [ComponentType.HistogramPanel]: null,
        [ComponentType.DataTablePanel]: null,
        [ComponentType.ScatterPlotPanel]: null,
        [ComponentType.XbarRbarPanel]: null,
      });

      // Reset the flag after a short delay to allow all state updates to complete
      setTimeout(() => {
        setIsFileSelectionInProgress(false);
      }, 100);
    } catch (error) {
      console.error("Error fetching file data:", error);
      message.error("Failed to fetch file data");
      setIsFileSelectionInProgress(false); // Reset flag on error too
    }
  };

  // Fetch files when component mounts or when selectSystems changes
  useEffect(() => {
    fetchUploadedFiles();
  }, [selectSystems]);

  const fetchUploadedFiles = async () => {
    try {
      setLoadingFiles(true);
      const systemIdsString = selectSystems.length
        ? selectSystems[0].systems
            ?.map((system: { systemId: number }) => {
              return system.systemId;
            })
            .join(",")
        : "";
      const apiUrl = systemIdsString
        ? `/file?systems_id=${systemIdsString}`
        : "/file";
      const response = await getRequest(apiUrl);

      if (response.data && response.data.data.files) {
        setFiles(response.data.data.files);
        setLoadingFiles(false);
      }
    } catch (error: any) {
      console.error("Error fetching uploaded files:", error);
      if (error.status === 403) {
        auth.logout();
        // Notiflix.Notify.failure(error.response.data.message);
        message.error(error.response.data.message);
        return;
      }
      // Notiflix.Notify.failure('Failed to fetch uploaded files');
      message.error("Failed to fetch uploaded files");
      setLoadingFiles(false);
    }
  };

  // Handler for column selection
  const handleColumnSelection = (indices: number[], headers: string[]) => {
    setSelectedColumns({ indices, headers });

    // Update filter state with new column selection
    setPanelFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      Object.keys(newFilters).forEach((panelId) => {
        // Skip updating the OverviewPanel's own filter state
        if (panelId === "overview-panel") return;

        const panelFilterList = [...(newFilters[panelId] || [])];
        const columnFilter = panelFilterList.find((f) => f.type === "column");

        if (columnFilter) {
          // Update existing filter
          const updatedFilter = {
            ...columnFilter,
            selection: { indices, headers },
          };
          newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
        } else if (indices.length > 0) {
          // Add new filter
          newFilters[panelId] = [
            ...panelFilterList,
            createColumnFilter("column-filter", { indices, headers }),
          ];
        }
      });
      return newFilters;
    });
  };

  // Handler for date filter
  const handleDateFilter = (
    startDate: string | null,
    endDate: string | null
  ) => {
    // 🔍 ZOOM DEBUG: Log the date filter handler
    console.log('🔍 HANDLE DATE FILTER:', {
      timestamp: new Date().toISOString(),
      source: 'WorkFlowContainer.handleDateFilter',
      startDate: startDate,
      endDate: endDate,
      startDateType: typeof startDate,
      endDateType: typeof endDate,
      startDateAsDate: startDate ? new Date(startDate).toISOString() : null,
      endDateAsDate: endDate ? new Date(endDate).toISOString() : null,
      previousDateFilter: dateFilter
    });

    setDateFilter({ startDate, endDate });

    // Update filter state with new date filter
    setPanelFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      Object.keys(newFilters).forEach((panelId) => {
        const panelFilterList = [...(newFilters[panelId] || [])];
        const dateRangeFilter = panelFilterList.find((f) => f.type === "date");

        if (dateRangeFilter) {
          // Update existing filter
          const updatedFilter = {
            ...dateRangeFilter,
            startDate,
            endDate,
          };
          newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
        } else if (startDate && endDate) {
          // Add new filter
          newFilters[panelId] = [
            ...panelFilterList,
            createDateFilter("date-filter", startDate, endDate),
          ];
        }
      });
      return newFilters;
    });
  };

  // Handler for zoom selection in charts
  const handleValueRangeFilter = (
    column: string,
    min: any,
    max: any,
    sourcePanelId?: string,
    zoomData?: any
  ) => {
    // 🔍 ZOOM DEBUG: Log the zoom selection handler
    console.log('🔍 HANDLE VALUE RANGE FILTER:', {
      timestamp: new Date().toISOString(),
      source: 'WorkFlowContainer.handleValueRangeFilter',
      column: column,
      min: min,
      max: max,
      minType: typeof min,
      maxType: typeof max,
      minAsDate: min ? new Date(min).toISOString() : null,
      maxAsDate: max ? new Date(max).toISOString() : null,
      sourcePanelId: sourcePanelId,
      zoomData: zoomData
    });

    // If zoomData is provided with x1 and x2 (date range), update the dateFilter
    if (zoomData && zoomData.x1 && zoomData.x2) {
      console.log('🔍 CALLING handleDateFilter with:', {
        x1: zoomData.x1,
        x2: zoomData.x2,
        x1AsDate: new Date(zoomData.x1).toISOString(),
        x2AsDate: new Date(zoomData.x2).toISOString()
      });
      handleDateFilter(zoomData.x1, zoomData.x2);
    }

    // if(selectedFile?.data){
    //   const initialFiltered = getFilteredData(selectedFile.data ,column);
    //   console.log('initialFiltered 222222222222222222222222222222222222222:', initialFiltered);
    //   if(initialFiltered)
    //   setFilteredData(nitialFiltered);
    // }
    // Check if we're dealing with date strings
    // if (typeof min === 'string' && min.includes('-') && typeof max === 'string' && max.includes('-')) {
    //   // Use date filter mechanism for date ranges
    //   const startDate = min;
    //   const endDate = max;

    //   // Update the date filter state
    //   setDateFilter({ startDate, endDate });

    //   // Update filter state with new date filter
    //   setPanelFilters(prevFilters => {
    //     const newFilters = { ...prevFilters };
    //     Object.keys(newFilters).forEach(panelId => {
    //       // Skip the source panel to avoid circular filtering
    //       const sourceId = sourcePanelId || 'unknown-source';
    //       if (panelId === sourceId ||
    //           (sourcePanelId === 'time-series-panel' && panelId === 'time-series-panel') ||
    //           (sourcePanelId === 'histogram-panel' && panelId === 'histogram-panel')) {
    //         return;
    //       }

    //       const panelFilterList = [...(newFilters[panelId] || [])];
    //       const dateRangeFilter = panelFilterList.find(f => f.type === 'date');

    //       if (dateRangeFilter) {
    //         // Update existing filter
    //         const updatedFilter = {
    //           ...dateRangeFilter,
    //           startDate,
    //           endDate
    //         };
    //         newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
    //       } else {
    //         // Add new filter
    //         newFilters[panelId] = [
    //           ...panelFilterList,
    //           createDateFilter('date-filter', startDate, endDate)
    //         ];
    //       }
    //     });
    //     return newFilters;
    //   });

    //   return; // Exit early since we've handled the date filter case
    // }

    // // For numeric columns
    // setPanelFilters(prevFilters => {
    //   const newFilters = { ...prevFilters };
    //   const filterId = `value-range-${column}`;

    //   // Apply filter to all panels
    //   Object.keys(newFilters).forEach(panelId => {
    //     // Skip the source panel
    //     const sourceId = sourcePanelId || 'unknown-source';
    //     if (panelId === sourceId ||
    //         (sourcePanelId === 'time-series-panel' && panelId === 'time-series-panel') ||
    //         (sourcePanelId === 'histogram-panel' && panelId === 'histogram-panel')) {
    //       return;
    //     }

    //     const panelFilterList = [...(newFilters[panelId] || [])];

    //     // Find existing filter for this column
    //     const valueRangeFilter = panelFilterList.find(f =>
    //       f.type === 'value-range' && (f as ValueRangeFilter).column === column
    //     );

    //     if (valueRangeFilter) {
    //       // Update existing filter
    //       const updatedFilter = {
    //         ...valueRangeFilter,
    //         min,
    //         max
    //       };
    //       newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
    //     } else {
    //       // Add new filter
    //       newFilters[panelId] = [
    //         ...panelFilterList,
    //         createValueRangeFilter(filterId, column, min, max)
    //       ];
    //     }
    //   });

    //   return newFilters;
    // });
  };

  // Remove a filter from a specific panel or all panels if it's a conditional filter
  const handleRemoveFilter = (filterId: string, panelId: string) => {
    // If panelId is 'global', this is a global filter
    if (panelId === "global") {
      // Handle different filter types
      if (filterId === "date-filter") {
        // Clear date filter
        setDateFilter({ startDate: null, endDate: null });
      } else if (filterId === "column-filter") {
        // Clear column selection
        setSelectedColumns({ indices: [], headers: [] });
      } else {
        // Remove a specific conditional filter
        setConditionalFilters((prevFilters) =>
          prevFilters.filter((filter) => filter.id !== filterId)
        );
      }
    } else {
      setConditionalFilters([]);
      // Handle panel-specific filters
      setPanelFilters((prevFilters) => {
        const panelFilters = prevFilters[panelId] || [];
        return {
          ...prevFilters,
          [panelId]: panelFilters.filter((filter) => filter.id !== filterId),
        };
      });
    }
  };

  // Handler for conditional column filters (from Handsontable)
  const handleConditionalFilter = (filter: PanelFilter) => {
    // Update the conditionalFilters state
    setConditionalFilters((prevFilters) => {
      // Check if this filter already exists (by column and operator)
      const existingFilterIndex = prevFilters.findIndex(
        (f) =>
          f.type === "conditional-column" &&
          (f as any).column === (filter as any).column &&
          (f as any).operator === (filter as any).operator
      );

      if (existingFilterIndex >= 0) {
        // Update existing filter
        const updatedFilters = [...prevFilters];
        updatedFilters[existingFilterIndex] = filter;
        return updatedFilters;
      } else {
        // Add new filter
        return [...prevFilters, filter];
      }
    });
  };

  // Track if we're in the middle of file selection to avoid logging during reset
  const [isFileSelectionInProgress, setIsFileSelectionInProgress] =
    useState(false);

  // Add a ref to prevent infinite loops from API calls
  const isApiCallInProgress = useRef(false);

  // Add a flag to prevent infinite loops when applying annotation/operation filters
  const isApplyingFiltersRef = useRef(false);

  // Add debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to handle annotation filter changes with flag management
  const handleAnnotationFilterToggle = useCallback(
    (columnName: string, shapeId: string) => {
      // Set the flag to prevent API calls during filter application
      isApplyingFiltersRef.current = true;

      // Dispatch the Redux action
      dispatch(
        toggleAnnotationFilter({
          columnName,
          shapeId,
        })
      );

      // Reset the flag after a short delay to allow state updates to complete
      setTimeout(() => {
        isApplyingFiltersRef.current = false;
      }, 500);
    },
    [dispatch]
  );

  // Helper function to handle operation filter changes with flag management
  const handleOperationFilterToggle = useCallback(
    (columnName: string, operationId: string) => {
      // Set the flag to prevent API calls during filter application
      isApplyingFiltersRef.current = true;

      // Dispatch the Redux action
      dispatch(
        toggleOperationFilter({
          columnName,
          operationId,
        })
      );

      // Reset the flag after a short delay to allow state updates to complete
      setTimeout(() => {
        isApplyingFiltersRef.current = false;
      }, 500);
    },
    [dispatch]
  );

  // Clear all filters
  const handleClearAllFilters = () => {
    // Clear filter states
    setSelectedColumns({ indices: [], headers: [] });
    setDateFilter({ startDate: null, endDate: null });

    // Clear conditional filters
    setConditionalFilters([]);

    // Clear panel filters
    setPanelFilters((prevFilters) => {
      const newFilters = { ...prevFilters };
      Object.keys(newFilters).forEach((panelId) => {
        newFilters[panelId] = [];
      });
      return newFilters;
    });

    // Clear annotation filters by setting applyAsFilter to false for all annotations
    if (annotationsState && annotationsState.annotations) {
      annotationsState.annotations.forEach(
        (group: { columnName: string; annotations: any[] }) => {
          group.annotations.forEach(
            (annotation: { applyAsFilter?: boolean; shapeId: string }) => {
              if (annotation.applyAsFilter) {
                // Use the toggleAnnotationFilter action to set applyAsFilter to false
                handleAnnotationFilterToggle(
                  group.columnName,
                  annotation.shapeId
                );
              }
            }
          );
        }
      );
    }

    // Clear operation filters by setting applyAsFilter to false for all operations
    if (operationsState && operationsState.operations) {
      operationsState.operations.forEach(
        (group: { columnName: string; operations: any[] }) => {
          group.operations.forEach(
            (operation: { applyAsFilter?: boolean; operationId: string }) => {
              if (operation.applyAsFilter) {
                // Use the toggleOperationFilter action to set applyAsFilter to false
                handleOperationFilterToggle(
                  group.columnName,
                  operation.operationId
                );

                // Find the full operation object from the operations state
                const fullOperation = group.operations.find(
                  (op: any) => op.operationId === operation.operationId
                );

                // When clearing filters, we want to make operations visible in the chart
                // So we need to check the checkbox if it's not already checked
                if (fullOperation) {
                  const selectedGroup = operationsState.selectedOperations.find(
                    (selGroup: any) => selGroup.columnName === group.columnName
                  );

                  const isAlreadySelected =
                    selectedGroup &&
                    selectedGroup.operations.some(
                      (op: any) => op.operationId === operation.operationId
                    );

                  // If not already selected, select it to make it visible in the chart
                  if (!isAlreadySelected) {
                    dispatch(
                      toggleSelectedOperation({
                        columnName: group.columnName,
                        operation: fullOperation,
                      })
                    );
                  }
                }
              }
            }
          );
        }
      );
    }

    console.log("All filters cleared");
  };

  const getFilteredData = (
    data: any,
    zoomData?: any,
    annotationsStateOverride?: any,
    operationsStateOverride?: any
  ) => {
    if (!data) return null;

    let filteredData = [...data]; // Create a copy of the original data

    // Apply zoom filters
    if (zoomData && zoomData.startDate && zoomData.endDate) {
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
      const dateColumnIndex = headers.findIndex(
        (header: string) => header.toLowerCase() === "datetime"
      );

      if (dateColumnIndex >= 0) {
        filteredData = filteredData.filter((row: any) => {
          const dateValue = Array.isArray(row)
            ? row[dateColumnIndex]
            : row[headers[dateColumnIndex]];

          if (!dateValue) return true;
          const rowDate = new Date(dateValue);
          const startDate = new Date(zoomData.startDate);
          const endDate = new Date(zoomData.endDate);

          return rowDate >= startDate && rowDate <= endDate;
        });
      }
    }

    // Apply date range filters
    if (dateFilter.startDate || dateFilter.endDate) {
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
      const dateColumnIndex = headers.findIndex(
        (header: string) => header.toLowerCase() === "datetime"
      );

      if (dateColumnIndex >= 0) {
        filteredData = filteredData.filter((row: any) => {
          if (row === data[0]) return true; // Keep header row

          const dateValue = Array.isArray(row)
            ? row[dateColumnIndex]
            : row[headers[dateColumnIndex]];

          if (!dateValue) return true;
          const rowDate = new Date(dateValue);

          if (dateFilter.startDate) {
            const startDate = new Date(dateFilter.startDate);
            if (rowDate < startDate) return false;
          }

          if (dateFilter.endDate) {
            const endDate = new Date(dateFilter.endDate);
            if (rowDate > endDate) return false;
          }

          return true;
        });
      }
    }

    // Apply conditional filters
    if (conditionalFilters.length > 0) {
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);

      conditionalFilters.forEach((filter: any) => {
        if (filter.type === "conditional-column") {
          const columnFilter = filter as any;
          const columnIndex = headers.findIndex(
            (header: string) => header === columnFilter.column
          );

          if (columnIndex >= 0) {
            filteredData = filteredData.filter((row: any) => {
              if (row === data[0]) return true; // Keep header row

              const cellValue = Array.isArray(row)
                ? row[columnIndex]
                : row[columnFilter.column];

              // Handle different operators
              switch (columnFilter.operator) {
                case "equals":
                  return cellValue == columnFilter.value;
                case "not equals":
                  return cellValue != columnFilter.value;
                case "contains":
                  return String(cellValue)
                    .toLowerCase()
                    .includes(String(columnFilter.value).toLowerCase());
                case "not contains":
                  return !String(cellValue)
                    .toLowerCase()
                    .includes(String(columnFilter.value).toLowerCase());
                case "greater than":
                  return parseFloat(cellValue) > parseFloat(columnFilter.value);
                case "less than":
                  return parseFloat(cellValue) < parseFloat(columnFilter.value);
                case "greater than or equal":
                  return (
                    parseFloat(cellValue) >= parseFloat(columnFilter.value)
                  );
                case "less than or equal":
                  return (
                    parseFloat(cellValue) <= parseFloat(columnFilter.value)
                  );
                default:
                  return true; // Unknown operator, don't filter
              }
            });
          }
        }
      });
    }

    // Use memoized annotation filters or override parameter
    const annotationsToUse =
      annotationsStateOverride || memoizedAnnotationFilters.filters;

    // Apply annotation filters from the memoized filters
    if (annotationsToUse && annotationsToUse.length > 0) {
      console.log("Applying annotation filters:", annotationsToUse);

      // Find date column for x-axis filtering
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
      const dateColumnIndex = headers.findIndex(
        (header: string) => header.toLowerCase() === "datetime"
      );

      // Apply each annotation filter
      annotationsToUse.forEach((annotation: any) => {
        const columnName = annotation.columnName;

        // Find the column index for the y-axis values
        const columnIndex = headers.findIndex(
          (header: string) => header === columnName
        );

        if (dateColumnIndex >= 0 && columnIndex >= 0) {
          // Convert annotation boundaries to proper types
          const x0Date = new Date(annotation.x0);
          const x1Date = new Date(annotation.x1);
          const y0Value = annotation.y0;
          const y1Value = annotation.y1;

          // Filter out data points that fall within the annotation boundaries
          filteredData = filteredData.filter((row: any) => {
            const dateValue = Array.isArray(row)
              ? row[dateColumnIndex]
              : row[headers[dateColumnIndex]];

            const yValue = Array.isArray(row)
              ? parseFloat(row[columnIndex])
              : parseFloat(row[columnName]);

            if (!dateValue || isNaN(yValue)) return true; // Keep rows with invalid data

            const rowDate = new Date(dateValue);

            // Check if the point falls within the annotation boundaries
            // For date range, check if the point is between x0 and x1
            const isInXRange = rowDate >= x0Date && rowDate <= x1Date;

            // For value range, check if the point is between the min and max values
            // Note: We need to handle the case where y0 might be greater than y1
            const minY = Math.min(y0Value, y1Value);
            const maxY = Math.max(y0Value, y1Value);
            const isInYRange = yValue >= minY && yValue <= maxY;

            // Return true to keep points that are OUTSIDE the annotation area
            return !(isInXRange && isInYRange);
          });
        }
      });
    }

    // Use memoized operation filters or override parameter
    const operationsToUse =
      operationsStateOverride || memoizedOperationFilters.filters;

    // Apply operation filters from the memoized filters
    if (operationsToUse && operationsToUse.length > 0) {
      console.log("Applying operation filters:", operationsToUse);

      // Find headers for column filtering
      const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);

      // Group operations by column name
      const operationsByColumn: Record<string, any[]> = {};
      operationsToUse.forEach((operation: any) => {
        const columnName = operation.columnName;
        if (!operationsByColumn[columnName]) {
          operationsByColumn[columnName] = [];
        }
        operationsByColumn[columnName].push(operation);
      });

      // Apply operation filters for each column separately
      Object.entries(operationsByColumn).forEach(
        ([columnName, operations]: [string, any[]]) => {
          // Find the column index for the y-axis values
          const columnIndex = headers.findIndex(
            (header: string) => header === columnName
          );

          if (columnIndex >= 0) {
            // Apply each operation filter for this column
            operations.forEach((operation: any) => {
              // Get the operation boundaries
              const y0Value = operation.y0;
              const y1Value = operation.y1;

              // Get min and max values (in case y0 > y1)
              const minY = Math.min(y0Value, y1Value);
              const maxY = Math.max(y0Value, y1Value);

              console.log(
                `Applying operation filter for column ${columnName} with range: ${minY} - ${maxY}`
              );

              // Filter data points - KEEP only points that are WITHIN the operation range
              // This is the opposite of annotation filtering
              filteredData = filteredData.filter((row: any) => {
                const yValue = Array.isArray(row)
                  ? parseFloat(row[columnIndex])
                  : parseFloat(row[columnName]);

                if (isNaN(yValue)) return false; // Remove rows with invalid data

                // Check if the point falls within the operation boundaries
                const isInYRange = yValue >= minY && yValue <= maxY;

                // Return true to keep points that are INSIDE the operation range
                return isInYRange;
              });
            });
          }
        }
      );
    }

    return filteredData;
  };

  // Create initial panels when a file is selected
  useEffect(() => {
    if (selectedFile && !initialPanelsCreated) {
      setInitialPanelsCreated(true);
    }
  }, [selectedFile, initialPanelsCreated]);

  // State for filtered data to ensure it updates when filters change
  const [filteredDataState, setFilteredDataState] = useState<any>(null);

  // Panel data for batch exploration (PLC will have its own separate state when implemented)
  const [panelSpecificData, setPanelSpecificData] = useState<Record<ComponentType, any>>({
    [ComponentType.TimeSeriesPanel]: null,
    [ComponentType.OverviewPanel]: null,
    [ComponentType.HistogramPanel]: null,
    [ComponentType.DataTablePanel]: null,
    [ComponentType.ScatterPlotPanel]: null,
    [ComponentType.XbarRbarPanel]: null,
  });

  // Use the custom hook for API calls
  const {
    fetchAllPanelData,
    createFilterPayload,
    panelLoadingStates,
    panelErrorStates,
  } = useFetchFilteredFileData();



  // Memoize filter objects to prevent unnecessary re-renders
  const memoizedSelectedColumns = useMemo(
    () => selectedColumns,
    [selectedColumns.indices.join(","), selectedColumns.headers.join(",")]
  );

  const memoizedDateFilter = useMemo(
    () => dateFilter,
    [dateFilter.startDate, dateFilter.endDate]
  );

  const memoizedConditionalFilters = useMemo(() => {
    // Create a stable string representation for deep comparison
    const filterKey = conditionalFilters
      .map((filter) => {
        if (filter.type === "conditional-column") {
          const columnFilter = filter as any;
          return `${columnFilter.column}:${
            columnFilter.operator
          }:${JSON.stringify(columnFilter.value)}`;
        }
        return filter.id;
      })
      .sort()
      .join("|");

    return { filters: conditionalFilters, key: filterKey };
  }, [conditionalFilters]);

  // Add a ref to track the last filter state to prevent duplicate API calls
  const lastFilterStateRef = useRef<string>("");

  // Memoize annotation filters to prevent infinite loops
  const memoizedAnnotationFilters = useMemo(() => {
    if (!annotationsState?.annotations) {
      return { filters: [], key: "no-annotations" };
    }

    // Create stable filters array
    const filters = annotationsState.annotations.flatMap((group: any) =>
      group.annotations
        .filter((annotation: any) => annotation.applyAsFilter)
        .map((annotation: any) => ({
          id: annotation.shapeId,
          columnName: group.columnName,
          x0: annotation.x0,
          x1: annotation.x1,
          y0: annotation.y0,
          y1: annotation.y1,
        }))
    );

    // Create stable key that only changes when relevant properties change
    const key = filters
      .map(
        (f: any) => `${f.id}:${f.columnName}:${f.x0}:${f.x1}:${f.y0}:${f.y1}`
      )
      .sort()
      .join("|");

    return { filters, key };
  }, [annotationsState?.annotations]);

  // Memoize operation filters to prevent infinite loops
  const memoizedOperationFilters = useMemo(() => {
    if (!operationsState?.operations) {
      return { filters: [], key: "no-operations" };
    }

    // Create stable filters array
    const filters = operationsState.operations.flatMap((group: any) =>
      group.operations
        .filter((operation: any) => operation.applyAsFilter)
        .map((operation: any) => ({
          id: operation.operationId,
          columnName: group.columnName,
          y0: operation.y0,
          y1: operation.y1,
        }))
    );

    // Create stable key that only changes when relevant properties change
    const key = filters
      .map((f: any) => `${f.id}:${f.columnName}:${f.y0}:${f.y1}`)
      .sort()
      .join("|");

    return { filters, key };
  }, [operationsState?.operations]);

  // Function to fetch data for a single newly added panel
  const fetchSinglePanelData = useCallback(async (panelType: ComponentType) => {
    if (!selectedFile || flowType !== "exploration") {
      return;
    }

    try {
      // Convert dateFilter to GMT format for backend
      const processedDateFilter = {
        startDate: dateFilter.startDate ? (
          typeof dateFilter.startDate === 'number'
            ? new Date(dateFilter.startDate).toISOString()
            : dateFilter.startDate
        ) : null,
        endDate: dateFilter.endDate ? (
          typeof dateFilter.endDate === 'number'
            ? new Date(dateFilter.endDate).toISOString()
            : dateFilter.endDate
        ) : null
      };

      // 🔍 ZOOM DEBUG: Log the date filter processing
      console.log('🔍 PROCESSED DATE FILTER:', {
        timestamp: new Date().toISOString(),
        source: 'WorkFlowContainer.fetchSinglePanelData',
        originalDateFilter: dateFilter,
        processedDateFilter: processedDateFilter
      });

      const payload = createFilterPayload(
        selectedFile.csv_id,
        selectedFile.file_name || "unknown",
        selectedColumns,
        processedDateFilter,
        conditionalFilters,
        memoizedAnnotationFilters.filters,
        memoizedOperationFilters.filters
      );

      // 🔍 ZOOM DEBUG: Log the final payload
      console.log('🔍 FINAL API PAYLOAD (fetchSinglePanelData):', {
        timestamp: new Date().toISOString(),
        source: 'WorkFlowContainer.fetchSinglePanelData',
        payload: payload,
        payloadStringified: JSON.stringify(payload, null, 2)
      });

      // Add panel configuration to payload if available
      if (panelConfigurations[panelType]) {
        payload.configuration = panelConfigurations[panelType];

        // For ScatterPlotPanel, check if target variable is configured
        if (panelType === ComponentType.ScatterPlotPanel && !payload.configuration.targetVariable) {
          // Set empty data with error message
          setPanelSpecificData((prevData) => ({
            ...prevData,
            [panelType]: {
              columnOptions: {},
              metadata: {
                hasData: false,
                totalColumns: 0,
                processedColumns: 0,
                error: "No target variable selected. Please configure a target variable to plot scatter plot."
              }
            },
          }));
          return;
        }
      } else if (panelType === ComponentType.ScatterPlotPanel) {
        // No configuration for ScatterPlot panel, skip API call
        setPanelSpecificData((prevData) => ({
          ...prevData,
          [panelType]: {
            columnOptions: {},
            metadata: {
              hasData: false,
              totalColumns: 0,
              processedColumns: 0,
              error: "No target variable selected. Please configure a target variable to plot scatter plot."
            }
          },
        }));
        return;
      }
      // Call API for the single panel
      await fetchAllPanelData([panelType], payload, (panelType, data) => {
        setPanelSpecificData((prevData) => ({
          ...prevData,
          [panelType]: data,
        }));
      });
    } catch (error) {
      console.error(`Error fetching data for ${panelType}:`, error);
    }
  }, [selectedFile, flowType, createFilterPayload, selectedColumns, dateFilter, conditionalFilters, memoizedAnnotationFilters.filters, memoizedOperationFilters.filters, fetchAllPanelData, panelConfigurations]);

  // Function to handle panel removal - clean up panel data
  const handlePanelRemoval = useCallback((panelType: ComponentType) => {
    setPanelSpecificData((prevData) => ({
      ...prevData,
      [panelType]: null,
    }));
  }, []);

  // Function to handle panel configuration changes
  const handlePanelConfigurationChange = useCallback((panelType: ComponentType, config: any) => {
    // Update panel configuration
    setPanelConfigurations(prev => ({
      ...prev,
      [panelType]: config
    }));

    // Trigger data refetch for this specific panel
    fetchSinglePanelData(panelType);
  }, [fetchSinglePanelData]);

  // Memoized callback for handling API calls
  const handleApiCall = useCallback(async () => {
    // Prevent infinite loops by checking if API call is already in progress
    if (isApiCallInProgress.current) {
      return;
    }

    // Don't make API calls during file selection process
    if (isFileSelectionInProgress) {
      return;
    }

    // Don't make API calls when applying filters programmatically
    if (isApplyingFiltersRef.current) {
      console.log(
      );
      return;
    }

    // Only proceed if we have a selected file and are in exploration mode
    if (!selectedFile || flowType !== "exploration") {
      return;
    }

    // Skip API calls if no panels are active
    if (!activePanels || activePanels.length === 0) {
      return;
    }

    // Create a unique key for the current filter state to detect actual changes
    // Note: selectedColumns is excluded as it only affects visual representation, not data filtering
    const currentFilterState = `${
      selectedFile.csv_id
    }:${memoizedDateFilter.startDate || ""}:${
      memoizedDateFilter.endDate || ""
    }:${memoizedConditionalFilters.key}:${memoizedAnnotationFilters.key}:${
      memoizedOperationFilters.key
    }:${activePanels.join(",")}`;

    // Check if filters have actually changed
    if (lastFilterStateRef.current === currentFilterState) {
      return;
    }

    // Update the last filter state
    lastFilterStateRef.current = currentFilterState;

    try {
      // Set the ref to prevent concurrent API calls
      isApiCallInProgress.current = true;

      // Convert dateFilter to GMT format for backend
      const processedDateFilter = {
        startDate: memoizedDateFilter.startDate ? (
          typeof memoizedDateFilter.startDate === 'number'
            ? new Date(memoizedDateFilter.startDate).toISOString()
            : memoizedDateFilter.startDate
        ) : null,
        endDate: memoizedDateFilter.endDate ? (
          typeof memoizedDateFilter.endDate === 'number'
            ? new Date(memoizedDateFilter.endDate).toISOString()
            : memoizedDateFilter.endDate
        ) : null
      };

      // 🔍 ZOOM DEBUG: Log the date filter processing for main API call
      console.log('🔍 PROCESSED DATE FILTER (Main API):', {
        timestamp: new Date().toISOString(),
        source: 'WorkFlowContainer.useEffect[main API call]',
        originalMemoizedDateFilter: memoizedDateFilter,
        processedDateFilter: processedDateFilter
      });

      const payload = createFilterPayload(
        selectedFile.csv_id,
        selectedFile.file_name || "unknown",
        memoizedSelectedColumns,
        processedDateFilter,
        memoizedConditionalFilters.filters,
        memoizedAnnotationFilters.filters,
        memoizedOperationFilters.filters
      );

      // 🔍 ZOOM DEBUG: Log the final payload for main API call
      console.log('🔍 FINAL API PAYLOAD (Main API):', {
        timestamp: new Date().toISOString(),
        source: 'WorkFlowContainer.useEffect[main API call]',
        payload: payload,
        payloadStringified: JSON.stringify(payload, null, 2)
      });

      console.log(
        payload
      );

      // Call API to fetch filtered data only for active panels
      await fetchAllPanelData(activePanels, payload, (panelType, data) => {
        setPanelSpecificData((prevData) => ({
          ...prevData,
          [panelType]: data,
        }));
      });
    } catch (error) {
      console.error("Error in API call:", error);
      // Reset the filter state on error so it can be retried
      lastFilterStateRef.current = "";
    } finally {
      // Always reset the ref when API calls are complete
      isApiCallInProgress.current = false;
    }
  }, [
    selectedFile?.csv_id,
    selectedFile?.file_name,
    flowType,
    // memoizedSelectedColumns removed - column selection only affects visual representation
    memoizedDateFilter,
    memoizedConditionalFilters.key, // Use the key instead of the full object
    memoizedAnnotationFilters.key, // Use the key instead of the full object
    memoizedOperationFilters.key, // Use the key instead of the full object
    isFileSelectionInProgress,
    createFilterPayload,
    fetchAllPanelData,
  ]);

  // Debounced effect for API calls
  useEffect(() => {
    // Clear any existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new timer with 300ms delay
    debounceTimerRef.current = setTimeout(() => {
      handleApiCall();
    }, 300); // 300ms debounce delay

    // Cleanup function to clear timer on unmount or dependency change
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [handleApiCall]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const goldenParameter = params.get("golden-parameter");
    if (goldenParameter == "true") {
      setDataContentSection("golden-parameters");
    } else {
      setDataContentSection("file-listing");
    }
  }, []);

  useEffect(() => {
    const currentUrl = window.location.pathname;
    const search = window.location.search;
    const queryParams = new URLSearchParams(search);

    if (
      queryParams.has("golden-parameter") &&
      queryParams.get("golden-parameter") === "true" &&
      queryParams.get("tab") !== "data"
    ) {
      setDataContentSection("file-listing");
      queryParams.delete("golden-parameter");

      const updatedSearch = queryParams.toString();
      const newUrl = `${currentUrl}${updatedSearch ? `?${updatedSearch}` : ""}`;

      window.history.replaceState({}, "", newUrl);
    }
  }, [window.location.pathname, window.location.search]);

  // Handler to save the query for a specific file
  const handleSaveQuery = (query: any, fileName: string) => {
    setQueries((prevQueries) => ({
      ...prevQueries,
      [fileName]: query,
    }));
    setShowQueryBuilder(false);
    console.log(`Query for ${fileName} saved:`, query);
  };

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
    // setReloadSelectSystems(false)
  }, []);

  const onDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    // setReloadSelectSystems(false)
    const data = JSON.parse(
      event.dataTransfer.getData("application/reactflow")
    );
    const dropPosition = {
      x: event.nativeEvent.offsetX,
      y: event.nativeEvent.offsetY,
    };

    const newNode: Node = {
      id: `${data.type}-${Date.now()}`,
      type: "custom",
      position: dropPosition,
      data: {
        label: data?.name,
        id: data?.id,
        type: data?.type,
        file_path: data?.data?.file_path,
        columns: data?.data?.columns,
        isConnected: false,
        onFilterClick: () =>
          handleNodeFilterClick(data.name, data.type, data.columns),
      },
    };

    setNodes((prevNodes) => [...prevNodes, newNode]);
  }, []);

  const handleNodeFilterClick = (
    fileName: string,
    nodeType: string,
    columns: []
  ) => {
    if (nodeType === "file") {
      if (currentFile && currentFile !== fileName && fileQueries[currentFile]) {
        const shouldSave = window.confirm(
          "Do you want to save changes to the current query?"
        );
        if (shouldSave) {
          handleSaveQueryBuilder();
        }
      }
      setCurrentFile(fileName);
      setShowQueryBuilder(true);
      setShowOperationConfig(false);
    } else if (nodeType === "operation") {
      setShowOperationConfig(true);
      setShowQueryBuilder(false);
    }
  };

  const renderTabs = () => {
    const params = new URLSearchParams(location.search);
    const workflowId = params.get("workflowId");
    let showFolderComponent = false;
    if (flowType == "view") {
      showFolderComponent = viewTabOpen;
      console.log("viewTabOpen :", viewTabOpen);
      // setViewTabOpen(false)
    }
    console.log("showFolderComponent :", showFolderComponent);
    if (workflowId === "0") {
      showFolderComponent = true;
    }
    console.log("showFolderComponent", showFolderComponent);
    const updateFilelist = (draggedFile: any) => {
      if ((draggedFile as any)?.csv_id) {
        setUploadedFiles((prevFiles: any[]) =>
          prevFiles.map((file: any) =>
            file.csv_id == (draggedFile as any)?.csv_id
              ? { ...file, compatibility: true }
              : file
          )
        );
      }
    };
    return (
      <>
        <div className={activeTab == "tab1" ? "visible" : "hidden"}>
          <DataTab
            workbookId={workbookId}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
            originalChartData={originalChartData}
            setOriginalChartData={setOriginalChartData}
            setFilteredChartData={setFilteredChartData}
            originalFileData={originalFileData}
            setOriginalFileData={setOriginalFileData}
            filteredChartData={filteredChartData}
            setFilteredFileData={setFilteredFileData}
            setShowVisualization={setShowVisualization}
            setActiveTab={setActiveTab}
            setDataContentSection={setDataContentSection}
            setDynamicPlotData={setDynamicPlotData}
            setDynamicPlotLayout={setDynamicPlotLayout}
            onFileUploadSuccess={fetchUploadedFiles}
          />
        </div>
        <div
          className={
            activeTab == "tab2" && showFolderComponent ? "visible" : "hidden"
          }
        >
          <FolderStructure setViewTabOpen={setViewTabOpen} />
        </div>
        <div
          className={
            activeTab == "tab2" && !showFolderComponent ? "visible" : "hidden"
          }
        >
          {flowType === "view" ? (
            <div>
              <ExplorationSidebar
                files={files}
                selectedFile={selectedFile}
                onFileSelect={handleFileSelect}
                onBackClick={resetViewStates}
                activePanels={activePanels}
                isLoading={loadingFiles}
              />
            </div>
          ) : flowType === "exploration" && explorationType === "batch" ? (
            <div>
              <ExplorationSidebar
                files={files}
                selectedFile={selectedFile}
                onFileSelect={handleFileSelect}
                onBackClick={resetViewStates}
                activePanels={activePanels}
                isLoading={loadingFiles}
              />
            </div>
          ) : flowType === "exploration" && explorationType === "plc" ? (
            <div>
              <PLCSidebar
                onBackClick={resetViewStates}
                activePanels={activePanels as any}
              />
            </div>
          ) : (
            <div>
              <InsightTab nodes={nodes} updateFilelist={updateFilelist} />
            </div>
          )}

          {/* sidebar  */}
        </div>
        <div className={activeTab == "tab3" ? "visible" : "hidden"}>
          <AlertTab
            activeDashboard={activeDashboard}
            setActiveDashboard={setActiveDashboard}
          />
        </div>
      </>
    );
  };

  // Update active tab and workflowId based on URL params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get("tab");
    const workflowIdParam = params.get("workflowId");
    const viewIdParam = params.get("viewId");
    const fileParam = params.get("file");
    const dataExplorationParam = params.get("data-exploration");
    const explorationTypeParam = params.get("explorationType");
    const savedFile = localStorage.getItem("selectedFile");

    if (!tabParam) {
      navigate("/?tab=data", { replace: true });
      return; // Exit early since `navigate` will trigger the useEffect again
    }
    // Handle tab switching
    if (tabParam) {
      switch (tabParam) {
        case "insight":
          setActiveTab("tab2");
          if (viewIdParam) {
            setFlowType("view");
            break;
          }
          setFlowType("workflow");
          break;
        case "data":
          // Check if this is data exploration mode
          if (dataExplorationParam !== null) {
            setActiveTab("tab2");
            setFlowType("exploration");
            // Set exploration type, default to 'batch' if not specified
            const explType = explorationTypeParam || 'batch';
            setExplorationType(explType as 'batch' | 'plc');
            // Update URL to include explorationType if it wasn't specified
            if (!explorationTypeParam) {
              navigate(`/?tab=data&data-exploration&explorationType=batch`, { replace: true });
            }
          } else {
            setActiveTab("tab1");
            if (fileParam && !savedFile) {
              navigate("/?tab=data", { replace: true });
            }
          }
          break;
        case "dashboard":
          setActiveTab("tab3");
          break;
      }
    }

    // Set the workflow ID based on the URL param `workflowId`
    if (workflowIdParam) {
      const workflowId = Number(workflowIdParam);
      if (workflowId === 0) {
        setWorkbookId(0); // Indicate new workflow
      } else {
        setWorkbookId(workflowId); // Set existing workflow ID
        fetchWorkflowData(workflowId); // Fetch existing workflow data
      }
    }

    // Fetch view data if viewId is present in the URL (but not for data-exploration)
    if (viewIdParam && tabParam === "insight") {
      const viewId = Number(viewIdParam);
      if (viewId >= 0) {
        fetchViewData(0); // Always fetch latest view (viewId=0)
      }
    }

    // For data-exploration mode, only clear state when entering exploration for the first time
    // Don't clear state when just switching between batch and PLC exploration types
    if (
      tabParam === "data" &&
      dataExplorationParam !== null &&
      explorationTypeParam &&
      flowType !== "exploration" // Only reset if we're not already in exploration mode
    ) {
      setSelectedFile(null);
      setFileSelected(false);
      setInitialPanelsCreated(false);
      setActivePanels([]);
      setSelectedColumns({ indices: [], headers: [] });
      setDateFilter({ startDate: null, endDate: null });
      setViewStructure(null);
      setPanelFilters({});
      setConditionalFilters([]);
    }
    setShowOperationConfig(false);
    setShowQueryBuilder(false);
  }, [location.search]); // Re-run effect whenever URL changes

  // Fetch workflow data from the API if the workflowId is greater than 0
  const fetchWorkflowData = async (workflowId: number) => {
    try {
      if (workflowId) {
        // You can update state with the fetched workflow data here
        // For example, setting files or other workflow-related data:
        // setUploadedFiles([]);
        // set([]);
        // Handle other state updates as needed based on the fetched data
      }
    } catch (error) {
      console.error("Error fetching workflow data:", error);
      // Notiflix.Notify.failure("Failed to fetch workflow data");
      message.error("Failed to fetch workflow data");
    }
  };

  // State for view structure
  const [viewStructure, setViewStructure] = useState<any>(null);

  // Fetch view data from the API by viewId (now using pannel API)
  const fetchViewData = async (viewId: number) => {
    try {
      // Notiflix.Loading.circle('Loading view...');
      showFullscreenLoader();
      const response = await getRequest(`/pannel/view/${viewId}`);

      if (response.data) {
        const viewData = response.data.data;
        dispatch(
          addTab({
            fileType: "ts",
            id: response.data.data.id,
            viewName: response.data.data.name,
          })
        );

        // Get the file data for the view
        if (viewData.csvfile_id) {
          const fileData: any = {
            csv_id: viewData.csvfile_id,
          };

          // Fetch the file data if needed
          const fileResponse = await getRequest(`/file/${viewData.csvfile_id}`);
          if (fileResponse.data) {
            fileData.data = fileResponse.data.data;
          }

          // Set the selected file
          setSelectedFile(fileData);
          setFileSelected(true);

          // Get the panel types from the view panels and extract filters
          const panelTypes: ComponentType[] = [];
          let savedPanelFiltersConfig: any = null;
          const savedPanelFilters: Record<string, PanelFilter[]> = {};

          if (viewData.viewPanels && viewData.viewPanels.length > 0) {
            viewData.viewPanels.forEach((panel: any) => {
              const panelTypeId = panel.pannel_type_id;
              const componentType =
                getComponentTypeFromPanelTypeId(panelTypeId);
              if (componentType) {
                panelTypes.push(componentType);
              }

              // Extract panel configuration and filters
              if (panel.configuration) {
                // Get panel ID from configuration (individual panel saving stores panelId)
                const panelId = panel.configuration.panelId;

                if (panelId) {
                  // Check for new appliedFilters structure first
                  if (panel.configuration.appliedFilters) {
                    const appliedFilters = panel.configuration.appliedFilters;

                    // Extract panel-specific filters
                    if (
                      appliedFilters.panelFilters &&
                      Array.isArray(appliedFilters.panelFilters)
                    ) {
                      savedPanelFilters[panelId] = appliedFilters.panelFilters;
                    }

                    // Extract global filters - merge from all panels to get the most comprehensive set
                    if (!savedPanelFiltersConfig) {
                      savedPanelFiltersConfig = appliedFilters;
                    } else {
                      // Merge filters from multiple panels
                      if (
                        appliedFilters.selectedColumns &&
                        appliedFilters.selectedColumns.indices.length > 0
                      ) {
                        savedPanelFiltersConfig.selectedColumns =
                          appliedFilters.selectedColumns;
                      }
                      if (
                        appliedFilters.dateFilter &&
                        (appliedFilters.dateFilter.startDate ||
                          appliedFilters.dateFilter.endDate)
                      ) {
                        savedPanelFiltersConfig.dateFilter =
                          appliedFilters.dateFilter;
                      }
                      if (
                        appliedFilters.conditionalFilters &&
                        appliedFilters.conditionalFilters.length > 0
                      ) {
                        savedPanelFiltersConfig.conditionalFilters =
                          appliedFilters.conditionalFilters;
                      }
                    }
                  } else {
                    // Fallback to old structure for backward compatibility
                    // Extract panel-specific filters
                    if (panel.configuration.filters) {
                      savedPanelFilters[panelId] = panel.configuration.filters;
                    }

                    // Extract global filters from the first panel that has them
                    if (
                      !savedPanelFiltersConfig &&
                      panel.configuration.panelFilters
                    ) {
                      savedPanelFiltersConfig =
                        panel.configuration.panelFilters;
                    }
                  }

                  // Ensure structure includes this panel
                  const existingStructureItem = viewData.structure?.find(
                    (item: any) => item.i === panelId
                  );
                  if (!existingStructureItem && panel.configuration.position) {
                    // Add missing structure item from panel configuration
                    if (!viewData.structure) viewData.structure = [];
                    viewData.structure.push({
                      i: panelId,
                      x: panel.configuration.position.x || 0,
                      y: panel.configuration.position.y || 0,
                      w: panel.configuration.position.w || 6,
                      h: panel.configuration.position.h || 6,
                      panelType: componentType,
                      minW: 3,
                      minH: 5,
                    });
                  }
                }
              }
            });
          }

          // Set the active panels
          setActivePanels(panelTypes);

          // Apply saved filters if they exist
          if (savedPanelFiltersConfig) {
            console.log("Loading saved filters:", savedPanelFiltersConfig);

            // Apply selected columns
            if (savedPanelFiltersConfig.selectedColumns) {
              console.log(
                "Loading selected columns:",
                savedPanelFiltersConfig.selectedColumns
              );
              setSelectedColumns(savedPanelFiltersConfig.selectedColumns);
            } else {
              // Clear selected columns if none are saved
              setSelectedColumns({ indices: [], headers: [] });
            }

            // Apply date filter
            if (
              savedPanelFiltersConfig.dateFilter &&
              (savedPanelFiltersConfig.dateFilter.startDate ||
                savedPanelFiltersConfig.dateFilter.endDate)
            ) {
              console.log(
                "Loading date filter:",
                savedPanelFiltersConfig.dateFilter
              );
              setDateFilter(savedPanelFiltersConfig.dateFilter);
            } else {
              // Clear date filter if none are saved
              setDateFilter({ startDate: null, endDate: null });
            }

            // Apply conditional filters if they exist
            if (
              savedPanelFiltersConfig.conditionalFilters &&
              Array.isArray(savedPanelFiltersConfig.conditionalFilters) &&
              savedPanelFiltersConfig.conditionalFilters.length > 0
            ) {
              console.log(
                "Loading conditional filters:",
                savedPanelFiltersConfig.conditionalFilters
              );
              setConditionalFilters(savedPanelFiltersConfig.conditionalFilters);
            } else {
              // Clear conditional filters if none are saved
              setConditionalFilters([]);
            }
          } else {
            console.log("No saved filters found, clearing all filters");
            // Clear all filters if no saved config
            setSelectedColumns({ indices: [], headers: [] });
            setDateFilter({ startDate: null, endDate: null });
            setConditionalFilters([]);
          }

          // Apply panel-specific filters
          if (Object.keys(savedPanelFilters).length > 0) {
            setPanelFilters(savedPanelFilters);
          }

          // Set the layout structure
          if (viewData.structure) {
            // The structure will be used by GridLayout component
            console.log("Setting structure:", viewData.structure);
            setViewStructure(viewData.structure);
          }

          // Set initialPanelsCreated to true to prevent automatic panel creation
          setInitialPanelsCreated(true);
        } else {
          setSelectedFile(null);
          setFileSelected(false);
          setInitialPanelsCreated(false);
          setActivePanels([]);
          setSelectedColumns({ indices: [], headers: [] });
          setDateFilter({ startDate: null, endDate: null });
          setViewStructure(null);
        }
        // Notiflix.Loading.remove();
        hideFullscreenLoader();
      } else {
        message.error("Failed to fetch view data");
        // Notiflix.Loading.remove();
        hideFullscreenLoader();
      }
    } catch (error) {
      console.error("Error fetching view data:", error);
      message.error("Failed to fetch view data");
      // Notiflix.Loading.remove();
      hideFullscreenLoader();
    }
  };

  // Helper function to map panel type ID to component type
  const getComponentTypeFromPanelTypeId = (
    panelTypeId: number
  ): ComponentType | null => {
    switch (panelTypeId) {
      case 1:
        return ComponentType.TimeSeriesPanel;
      case 2:
        return ComponentType.OverviewPanel;
      case 3:
        return ComponentType.HistogramPanel;
      case 4:
        return ComponentType.DataTablePanel;
      default:
        return null;
    }
  };

  const handleFileDoubleClick = async (csvId: string) => {
    try {
      const response = await getRequest(`/file/${csvId}`);
      if (response.data.status === 200) {
        const fileData = response.data.data;
          const cleanedData = fileData.map((row: any) => {
          const cleanRow = { ...row };
          Object.keys(cleanRow).forEach((key) => {
            // Only try to parse Time if it exists
            if (key === "Time" && cleanRow[key]) {
              // Keep Time as string for now
              cleanRow[key] = cleanRow[key];
            } else if (key === "DateTime" && cleanRow[key]) {
              cleanRow[key] = cleanRow[key];
            } else if (typeof cleanRow[key] === "string") {
              // Convert other string numbers to actual numbers
              const num = parseFloat(cleanRow[key]);
              cleanRow[key] = isNaN(num) ? cleanRow[key] ? cleanRow[key] : 0 : num;
            }
          });
          return cleanRow;
        });

        setOriginalFileData(cleanedData);
        setFilteredFileData(cleanedData);
        setDataContentSection("data-visualisation");

        // Update URL to include the file ID
        // navigate(`/?tab=data&file=${csvId}`);
      }
    } catch (error) {
      console.error("Error fetching file data:", error);
      // Notiflix.Notify.failure('Failed to fetch file data');
      message.error("Failed to fetch file data");
    }
  };

  const backToFileListing = async () => {
    setDataContentSection("file-listing");
    // Remove file parameter from URL
    navigate("/?tab=data");
  };

  const handleSaveQueryBuilder = (queryData?: any) => {
    if (currentFile && queryData) {
      setNodes((prevNodes) =>
        prevNodes.map((node) => {
          if (node.data.label === currentFile) {
            return {
              ...node,
              data: {
                ...node.data,
                query: queryData,
                hasQueryConfig: true,
              },
            };
          }
          return node;
        })
      );
      console.log("Saving query for file:", currentFile, queryData);
    }
    setShowQueryBuilder(false);
  };

  // Add this function to check if user has access to dashboard
  const hasDashboardAccess = () => {
    const allowedUsers = [
      "coca",
      "chemical",
      "dairy",
      "carbonblack_processengineer",
      "cement",
    ];
    return allowedUsers.includes(
      authState?.user?.first_name?.toLowerCase() || ""
    );
  };

  // Update the tab click handlers to only include workflowId for insight tab
  const handleTabClick = (tab: string, urlParam: string) => {
    setActiveTab(tab);

    if (urlParam === "insight") {
      // For insight tab, include workflowId
      const params = new URLSearchParams(location.search);
      const workflowId = params.get("workflowId") || "0";
      const viewId = params.get("viewId");
      if (viewId) {
        setFlowType("view");
        return;
      }
      setFlowType("workflow");
      navigate(`/?tab=insight&workflowId=${workflowId}`);
    } else {
      // For data and dashboard tabs, only include tab parameter
      navigate(`/?tab=${urlParam}`);
      setDataContentSection("file-listing");
    }
  };

  const handleTabChange = (tabId: any) => {
    setActiveTab(tabId);

    const params = new URLSearchParams(window.location.search);
    let currentTab =
      tabId == "tab1"
        ? "data"
        : tabId == "tab2"
        ? "insight"
        : tabId == "tab3"
        ? "dashboard"
        : "insight";
    params.set("tab", currentTab);
    window.history.pushState({}, "", `?${params.toString()}`);
  };

  return (
    <Flex
      flex={1}
      align="start"
      className={
        collapse
          ? "sidebar-remove relative sidebar-outer"
          : "relative sidebar-outer"
      }
    >
      {/* Tabs Section */}
      <div className="insight-tabs">
        {/* Tab Buttons */}
        <Flex justify="space-between" className="tabs-header">
          <button
            onClick={() => handleTabClick("tab1", "data")}
            style={
              activeTab === "tab1"
                ? {
                    color: "#000",
                    background: "#E9E9F5",
                    borderBottomColor: "#32377F",
                  }
                : {}
            }
          >
            <img src={data} alt="data" /> Data
          </button>
          <button
            onClick={() => handleTabClick("tab2", "insight")}
            style={
              activeTab === "tab2"
                ? {
                    color: "#000",
                    background: "#E9E9F5",
                    borderBottomColor: "#32377F",
                  }
                : {}
            }
          >
            <img src={insight} alt="insight" /> Insight
          </button>
          {/* Only show dashboard button for authorized users */}
          {hasDashboardAccess() && (
            <button
              onClick={() => handleTabClick("tab3", "dashboard")}
              style={
                activeTab === "tab3"
                  ? {
                      color: "#000",
                      background: "#E9E9F5",
                      borderBottomColor: "#32377F",
                    }
                  : {}
              }
            >
              <img src={box} alt="dashboard" /> Dashboard
            </button>
          )}
        </Flex>

        {/* Tabs Body */}
        <Flex vertical className="tab-content">
          <button
            className={collapse ? "active arrow-btn1" : "arrow-btn1"}
            onClick={() => setCollapse(!collapse)}
          >
            <img src={arrowleft} alt="arrow left" />
          </button>
          <div className="tab-inner">{renderTabs()}</div>
        </Flex>
      </div>

      {/* Tabs Content */}
      <Flex className="common-box !p-0" flex={1} vertical>
        {/* {activeTab === "tab1" && ( */}
        <div className={`${activeTab === "tab1" ? "block" : "hidden"}`}>
          <DataTabContent
            dataContentSection={dataContentSection}
            setDataContentSection={setDataContentSection}
            setUploadedFiles={setUploadedFiles}
            handleFileDoubleClick={handleFileDoubleClick}
            file={file}
            uploadedFiles={uploadedFiles}
            filteredFileData={filteredFileData}
            setFilteredFileData={setFilteredFileData}
            dynamicPlotData={dynamicPlotData}
            setDynamicPlotData={setDynamicPlotData}
            dynamicPlotLayout={dynamicPlotLayout}
            setDynamicPlotLayout={setDynamicPlotLayout}
            onFileUploadSuccess={fetchUploadedFiles}
          />
        </div>
        {/* {activeTab === "tab2" && ( */}
        <div
          className={`${
            activeTab === "tab2" ? "block" : "!hidden"
          } work-book-box !border-none`}
          style={{ height: "100%" }}
        >
          <div
            className="component-data"
            style={{ height: "calc(100% - 56px)" }}
          >
            {flowType == "view" ? (
              <ExplorationContent
                selectedFile={selectedFile}
                filteredData={filteredDataState || selectedFile?.data}
                createInitialPanels={fileSelected && !initialPanelsCreated}
                onPanelsChange={setActivePanels}
                selectedColumns={selectedColumns}
                dateFilter={dateFilter}
                panelFilters={panelFilters}
                conditionalFilters={conditionalFilters}
                onColumnSelection={handleColumnSelection}
                onDateFilterChange={handleDateFilter}
                onZoomSelection={handleValueRangeFilter}
                onClearAllFilters={handleClearAllFilters}
                onAddFilter={handleConditionalFilter}
                onRemoveFilter={handleRemoveFilter}
                onPanelAdded={fetchSinglePanelData}
                onPanelRemoved={handlePanelRemoval}
                onPanelConfigurationChange={handlePanelConfigurationChange}
                panelConfigurations={panelConfigurations}
                structure={viewStructure}
              />
            ) : flowType == "exploration" && explorationType === "batch" ? (
              <ExplorationContent
                selectedFile={selectedFile}
                filteredData={filteredDataState || selectedFile?.data}
                panelSpecificData={panelSpecificData}
                panelLoadingStates={panelLoadingStates}
                panelErrorStates={panelErrorStates}
                isExplorationMode={true}
                createInitialPanels={fileSelected && !initialPanelsCreated}
                onPanelsChange={setActivePanels}
                selectedColumns={selectedColumns}
                dateFilter={dateFilter}
                panelFilters={panelFilters}
                conditionalFilters={conditionalFilters}
                onColumnSelection={handleColumnSelection}
                onDateFilterChange={handleDateFilter}
                onZoomSelection={handleValueRangeFilter}
                onClearAllFilters={handleClearAllFilters}
                onAddFilter={handleConditionalFilter}
                onRemoveFilter={handleRemoveFilter}
                onPanelAdded={fetchSinglePanelData}
                onPanelRemoved={handlePanelRemoval}
                onPanelConfigurationChange={handlePanelConfigurationChange}
                panelConfigurations={panelConfigurations}
                structure={null} // No saved structure for exploration mode
              />
            ) : flowType == "exploration" && explorationType === "plc" ? (
              <PLCContent
                activePanels={activePanels as any}
                onPanelsChange={setActivePanels as any}
              />
            ) : (
              <InsightTabContent
                nodes={nodes}
                setNodes={setNodes}
                onDrop={onDrop}
                onDragOver={onDragOver}
                showQueryBuilder={showQueryBuilder}
                setShowQueryBuilder={setShowQueryBuilder}
                onSaveQuery={handleSaveQuery}
                showOperationConfig={showOperationConfig}
                setShowOperationConfig={setShowOperationConfig}
                handleSaveQueryBuilder={handleSaveQueryBuilder}
                // setSelectSystems={setSelectSystems}
                // setReloadSelectSystems={setReloadSelectSystems}
              />
            )}
          </div>
        </div>

        <div className={`${activeTab == "tab3" ? "block" : "!hidden"}`}>
          <AlertTabContent activeDashboard={activeDashboard} />
        </div>
        {/* <div className={`${activeTab === "tab4" ? "block" : "!hidden"} work-book-box !border-none`} style={{ height: '100%' }}>
          <div className="component-data" style={{ height: 'calc(100% - 40px)' }}>
            <div>This is what we want </div>
          </div>
        </div> */}

        {/* {activeTab === "tab3" && (
          <>
            <AlertTabContent activeDashboard={activeDashboard}/>
          </>
        )} */}
      </Flex>
      <div className="overlay-text">R64.3.1-v202405071618-SNAPSHOT</div>
    </Flex>
  );
}

export default WorkbookContainer;
