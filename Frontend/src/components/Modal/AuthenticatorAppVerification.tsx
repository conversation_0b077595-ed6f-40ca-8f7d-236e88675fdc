import React, { useState, useRef, useEffect, ChangeEvent, KeyboardEvent, ClipboardEvent } from 'react';
import { Modal, Button, Typography, message } from 'antd';
import { MailOutlined, LockOutlined ,AppstoreOutlined} from '@ant-design/icons';
import { postRequest } from '../../utils/apiHandler';

const { Title, Text } = Typography;
interface EmailOtpModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (otpValue: any) => void;
  handleAnotherWay: () => void;
}
interface AuthenticatorAppProps {
  handleVerified: (otpValue: any) => void;
  handleAnotherWayVerification: () => void;
}

const EmailOtpModal: React.FC<EmailOtpModalProps> = ({ visible, onCancel, onSuccess ,handleAnotherWay}) => {
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const [loading, setLoading] = useState<boolean>(false);
//   const [codeSent, setCodeSent] = useState<boolean>(false); // NEW

  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

//   useEffect(() => {
//     if (!visible || !codeSent) return;

//     const timer = setInterval(() => {
//       setTimeLeft((prevTime) => {
//         if (prevTime <= 1) {
//           clearInterval(timer);
//           setResendDisabled(false);
//           return 0;
//         }
//         return prevTime - 1;
//       });
//     }, 1000);

//     return () => clearInterval(timer);
//   }, [visible, codeSent]);

//   const setUserDetails = () =>{
//     onSuccess()
//   }


  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number): void => {
    const value = e.target.value;

    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(0, 1);
    setOtp(newOtp);

    if (value && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number): void => {
    if (e.key === 'Backspace') {
      if (!otp[index] && index > 0) {
        const newOtp = [...otp];
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>): void => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();

    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split('');
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
    }
  };

//   const sendVerificationCode = async (): Promise<void> => {
//     setLoading(true);
//     try {
//       await new Promise((resolve) => setTimeout(resolve, 1000)); // simulate API
//       let user ={
//         email:email,
//         userData:userData
//       }
//       const response = await postRequest('/auth/send-otp',user)
//       if(response.status==200) message.success('Verification code sent!');
//       setCodeSent(true);
//       setOtp(['', '', '', '', '', '']);
//     } catch (error) {
//       message.error('Failed to send verification code.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleResendOtp = async (): Promise<void> => {
//     setLoading(true);
//     try {
//       await new Promise((resolve) => setTimeout(resolve, 1000)); // simulate API
//       let user ={
//         email:email,
//         userData:userData
//       }
//       const response = await postRequest('/auth/send-otp',user)
//       if(response.status==200) message.success('A new OTP has been sent to your email');
//         setOtp(['', '', '', '', '', '']);
//         setTimeLeft(resendEmailOTPTime);
//         setResendDisabled(true);
//     } catch (error) {
//       message.error('Failed to resend OTP. Please try again.');
//     } finally {
//       setLoading(false);
//     }
//   };

  const verifyOtp = async () => {
    const otpValue = otp.join('');
  
    if (otpValue.length !== 6) {
      message.error('Please enter a complete 6-digit OTP');
      return;
    }
    onSuccess(otpValue)
    return 
  };
  

//   useEffect(() => {
//     if (visible && codeSent) {
//       setTimeout(() => inputRefs.current[0]?.focus(), 100);
//     }
//   }, [visible, codeSent]);

  useEffect(() => {
    if (!visible) {
      setOtp(['', '', '', '', '', '']);
    //   setTimeLeft(resendEmailOTPTime);
    //   setResendDisabled(true);
    }
  }, [visible]);

  return (
        <>
      <div className="text-center">
        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <AppstoreOutlined style={{ fontSize: '24px', color: '#1890ff' }} />

          <Text className="block mb-1">Please enter the verification code from your authenticator app.</Text>
        </div>
          <>
          <form
            onSubmit={(e) => {
                e.preventDefault();
                verifyOtp();
            }}
            >
            <div className="px-2">
                <div className="mb-4">
                <div className="text-left mb-2">Enter 6-digit verification code</div>
                <div className="flex justify-between mb-4">
                    {otp.map((digit, index) => (
                    <input
                        key={index}
                        ref={(el) => (inputRefs.current[index] = el)}
                        type="text"
                        value={digit}
                        onChange={(e) => handleChange(e, index)}
                        onKeyDown={(e) => handleKeyDown(e, index)}
                        onPaste={index === 0 ? handlePaste : undefined}
                        className="w-12 h-12 text-center text-lg font-semibold 
                                border border-gray-300 rounded-lg
                                focus:border-blue-500 focus:ring-1 focus:ring-blue-500
                                focus:outline-none transition-all"
                        maxLength={1}
                        autoComplete="off"
                    />
                    ))}
                </div>
                </div>
                <div className="mb-4">
                    <button
                        type="submit"
                        className="w-full py-2 bg-primary text-white rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:ring-primary">
                        Verify and Continue
                    </button>
                </div>
                <div className="flex justify-end">
                <span className="text-blue-500 cursor-pointer" onClick={handleAnotherWay}>
                    Try Another Way
                </span>
                </div>
            </div>
            </form>
          </>
      </div>
      </>
  );
};


const AuthenticatorAppVerification: React.FC<AuthenticatorAppProps> = ({handleVerified ,handleAnotherWayVerification}) => {
// console.log('userData :', userData);
  const handleSuccess = (otpValue: any): void => {
  console.log('handleSuccess handleSuccess handleSuccess handleSuccess handleSuccess:',otpValue);
    handleVerified(otpValue); 
  };
  const handleAnotherWay = () =>{

    console.log(' handleAnotherWay handleAnotherWay handleAnotherWay handleAnotherWay :', );
    handleAnotherWayVerification()
  }

  return (
    <div className="flex items-center justify-center">
      <div className="w-full p-1 ">
        <EmailOtpModal
          visible={true}
          onCancel={() => console.log("Cancelled the authentication")}
          onSuccess={handleSuccess}
          handleAnotherWay={handleAnotherWay}
        />
      </div>
    </div>
  );
};

export default AuthenticatorAppVerification;
