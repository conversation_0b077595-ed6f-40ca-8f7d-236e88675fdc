import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ContextMenuState, Tab } from '../../components/Dashboard/DataExploration/Tabs/Types/tabTypes';

interface TabsState {
  tabs: Tab[];
  activeTabId: string | null;
  tabHistory: string[];
  draggedTabId: string | null;
  contextMenuState: ContextMenuState;
}

const initialState: TabsState = {
  tabs: [],
  activeTabId: null,
  tabHistory: [],
  draggedTabId: null,
  contextMenuState: {
    isVisible: false,
    x: 0,
    y: 0,
    tabId: ""
  }
};

const tabsSlice = createSlice({
  name: 'tabs',
  initialState,
  reducers: {
    addTab(state, action: PayloadAction<{ fileType: 'ts' | 'js' | 'jsx' | 'tsx', id: any, viewName: any }>) {
        const { fileType, viewName, id: viewId } = action.payload;
      
        // If a tab with the same ID already exists, just activate it
        const existingTab = state.tabs.find(tab => tab.id == viewId);
        if (existingTab) {
          state.tabs = state.tabs.map(tab => ({
            ...tab,
            active: tab.id === viewId
          }));
          state.activeTabId = viewId;
          state.tabHistory = [viewId, ...state.tabHistory.filter(id => id != viewId)];
          return;
        }
      
        const newTab: Tab = {
          id: viewId,
          label: viewName, // Use viewName directly for display
          icon: fileType,
          content: '',
          active: true,
          modified: false
        };

        state.tabs = state.tabs.map(tab => ({ ...tab, active: false }));
        state.tabs.push(newTab);
        state.activeTabId = newTab.id;
        state.tabHistory = [newTab.id, ...state.tabHistory.filter(id => id !== newTab.id)];
      },
    setActiveTab(state, action: PayloadAction<string>) {
      const tabId = action.payload;
      state.activeTabId = tabId;
      state.tabs = state.tabs.map(tab => ({
        ...tab,
        active: tab.id === tabId
      }));
      state.tabHistory = [tabId, ...state.tabHistory.filter(id => id !== tabId)];
    },
    closeTab(state, action: PayloadAction<string>) {
      const tabId = action.payload;
      const tabIndex = state.tabs.findIndex(tab => tab.id === tabId);
      if (tabIndex === -1) return;

      let nextActiveTabId: string | null = null;
      if (state.activeTabId === tabId) {
        const filteredHistory = state.tabHistory.filter(id => id !== tabId && state.tabs.some(tab => tab.id === id));
        nextActiveTabId = filteredHistory.length > 0 ? filteredHistory[0] : null;

        if (!nextActiveTabId && state.tabs.length > 1) {
          const nextIndex = tabIndex === state.tabs.length - 1 ? tabIndex - 1 : tabIndex + 1;
          nextActiveTabId = state.tabs[nextIndex]?.id ?? null;
        }
      }

      state.tabs = state.tabs.filter(tab => tab.id !== tabId);
      state.tabHistory = state.tabHistory.filter(id => id !== tabId);
      if (state.activeTabId === tabId) {
        state.activeTabId = nextActiveTabId;
        state.tabs = state.tabs.map(tab => ({ ...tab, active: tab.id === nextActiveTabId }));
      }
    },
    closeAllTabs(state) {
      state.tabs = [];
      state.tabHistory = [];
      state.activeTabId = null;
    },
    closeOtherTabs(state, action: PayloadAction<string>) {
      const tabId = action.payload;
      state.tabs = state.tabs.filter(tab => tab.id === tabId);
      state.tabHistory = [tabId];
      state.activeTabId = tabId;
    },
    closeTabsToRight(state, action: PayloadAction<string>) {
      const tabId = action.payload;
      const index = state.tabs.findIndex(tab => tab.id === tabId);
      if (index === -1) return;

      const tabsToKeep = state.tabs.slice(0, index + 1);
      const closedTabIds = state.tabs.slice(index + 1).map(tab => tab.id);

      state.tabs = tabsToKeep;
      state.tabHistory = state.tabHistory.filter(id => !closedTabIds.includes(id));

      if (closedTabIds.includes(state.activeTabId!)) {
        state.activeTabId = tabId;
      }
    },
    startTabDrag(state, action: PayloadAction<string>) {
      state.draggedTabId = action.payload;
    },
    endTabDrag(state) {
      state.draggedTabId = null;
    },
    dropTab(state, action: PayloadAction<{ sourceId: string; targetId: string }>) {
      const { sourceId, targetId } = action.payload;
      const sourceIndex = state.tabs.findIndex(tab => tab.id == sourceId);
      const targetIndex = state.tabs.findIndex(tab => tab.id == targetId);
      if (sourceIndex == -1 || targetIndex == -1 || sourceId == targetId) return;

      const tabToMove = state.tabs[sourceIndex];
      state.tabs.splice(sourceIndex, 1);
      state.tabs.splice(targetIndex, 0, tabToMove);
    },
    showContextMenu(state, action: PayloadAction<{ x: number; y: number; tabId: string }>) {
      const { x, y, tabId } = action.payload;
      state.contextMenuState = {
        isVisible: true,
        x,
        y,
        tabId
      };
    },
    hideContextMenu(state) {
      state.contextMenuState.isVisible = false;
    }
  }
});

export const {
  addTab,
  setActiveTab,
  closeTab,
  closeAllTabs,
  closeOtherTabs,
  closeTabsToRight,
  startTabDrag,
  endTabDrag,
  dropTab,
  showContextMenu,
  hideContextMenu
} = tabsSlice.actions;

export default tabsSlice.reducer;
